import type { StorybookConfig } from "@storybook/react-webpack5";
import path from "path";

const config: StorybookConfig = {
  stories: ["../src/**/*.stories.@(tsx)"],
  addons: [
    "@storybook/addon-links",
    "@storybook/addon-essentials",
    "@storybook/addon-interactions",
    "@storybook/preset-create-react-app",
    "@storybook/addon-styling-webpack"
  ],
  framework: "@storybook/react-webpack5",
  docs: {
    autodocs: true
  },
  core: {
    builder: "@storybook/builder-webpack5",
    disableTelemetry: true
  },
  staticDirs: ["../public"],
  webpackFinal: async (config) => {
    if (!config.resolve) config.resolve = {};
    if (!config.resolve.fallback) config.resolve.fallback = {};
    config.resolve.fallback["zlib"] = false;

    // Add path mapping for @src/* to resolve TypeScript path aliases
    if (!config.resolve.alias) config.resolve.alias = {};

    // Map @src to the src directory
    const srcPath = path.resolve(__dirname, "../src");
    config.resolve.alias["@src"] = srcPath;

    // Also add specific mappings for common paths
    config.resolve.alias["@src/utils"] = path.resolve(srcPath, "utils");
    config.resolve.alias["@src/Translations"] = path.resolve(srcPath, "Translations");
    config.resolve.alias["@src/Information"] = path.resolve(srcPath, "Information");

    // Debug: log the alias configuration
    console.log("Webpack aliases:", config.resolve.alias);

    return config;
  }
};

export default config;

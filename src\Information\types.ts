export type Overwrite<T, U> = Omit<T, keyof U> & U;

export type ProfilePictureLabels = {
  title: string;
  message: string;
  avatarRequired: string;
  avatarInvalid: string;
  avatarMoreThanLimit: string;
  termsAndConditionsFirst: string;
  termsAndConditionsMiddle: string;
  termsAndConditionsLast: string;
};

export type ProfileLabels = {
  updateAvatar: string;
};

export type InfoLabels = {
  creatorSince: string;
  personalInformation: string;
  legalEntityType: string;
  legalEntityDescription: string;
  mailingAddress: string;
  miscellaneous: string;
  platformPreferences: string;
  primaryPlatform: string;
  platformPreferencesTitle: string;
  secondaryPlatform: string;
  secondaryPlatformTitle: string;
  header: {
    calendar: string;
  };
  info: {
    businessName: string;
  };
  success: {
    personalInformation: string;
    updatedInformationHeader: string;
    legalEntityType: string;
    mailingAddress: string;
    miscellaneous: string;
    platformPreferences: string;
  };
  labels: {
    individual: string;
    business: string;
    entityType: string;
    businessName: string;
    street: string;
    city: string;
    country: string;
    state: string;
    zipCode: string;
    legalAddressAsMailingAddress: string;
    tShirtSize: string;
    none: string;
    hardwarePartners: string;
    firstName: string;
    lastName: string;
    EAID: string;
    EAEmail: string;
    dateOfBirth: string;
  };
  messages: {
    firstName: string;
    firstNameTooLong: string;
    lastName: string;
    lastNameTooLong: string;
    dateOfBirth: string;
    dateOfBirthInvalid: string;
    ageMustBe18OrOlder: string;
    country: string;
    street: string;
    streetTooLong: string;
    city: string;
    cityTooLong: string;
    state: string;
    stateTooLong: string;
    zipCode: string;
    zipCodeTooLong: string;
    primaryPlatform: string;
    tShirtSize: string;
    hardwarePartners: string;
    entityType: string;
    businessName: string;
    businessNameTooLong: string;
    email: string;
    emailTooLong: string;
    emailInvalid: string;
    url: string;
    invalidUrl: string;
    duplicateUrl: string;
    urlScanFailed: string;
    followersMaxLength: string;
  };
  layout: {
    main: {
      unhandledError: string;
    }
  }
  profilePictureLabels: ProfilePictureLabels;
  profileLabels: ProfileLabels;
};

export type ButtonLabels = {
  edit?: string;
  cancel?: string;
  save?: string;
  close?: string;
  ok?: string;
  browse?: string;
};

.card-container {
  @apply grid grid-cols-2 items-center justify-center gap-x-meas10 text-black md:grid-cols-4 md:gap-y-meas6 xl:gap-x-[33px];
}
.card-col {
  @apply flex h-meas40 w-[130px] flex-col items-center justify-center xl:h-[18rem] xl:w-[15rem];
}
.card-col[data-disabled="true"] {
  @apply opacity-50;
}
.check-container {
  @apply relative block cursor-pointer select-none;
}
.check-container input {
  @apply absolute h-meas0 w-meas0 cursor-pointer opacity-0;
}
.checkmark {
  @apply left-meas0 top-meas0 flex h-[130px] w-[130px] items-center justify-center rounded-[3px] border-[3px] border-navy-60 bg-navy-80 bg-contain bg-center bg-no-repeat duration-300 ease-in-out xl:h-[231px] xl:w-[231px];
}
.checkmark .checkmark-box {
  @apply absolute right-meas0 top-meas0 fill-navy-60 text-gray-10;
}

.checkmark-layout {
  @apply absolute right-meas7 top-[10px] z-[1] hidden h-meas7 w-meas4 rotate-45 transform border-b-2 border-l-0 border-r-2 border-t-0 border-solid border-success-70;
  content: "";
  transform: rotate(45deg); /* somehow tailwind transform is not working */
}
.checkmark-layout.selected-card {
  @apply block;
}
.card-title {
  @apply flex w-full flex-col items-center justify-start px-meas4 text-center font-text-regular xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default xl:justify-center;
}
.checkmark:hover:not([data-disabled="true"]) {
  @apply hover:scale-105 hover:border-[3px] hover:border-gray-10 hover:shadow-gray-10-outline-10-opacity-0.7;
}
.check-container input:disabled ~ .checkmark {
  @apply border border-solid border-navy-60;
}
.check-container.selected-card .checkmark {
  @apply border-[3px] border-solid border-success-70;
}
.check-container.selected-card .checkmark:after {
  @apply border-[3px] border-solid border-success-70;
}
.check-container.selected-card .checkmark .checkmark-box {
  @apply fill-success-30 transition duration-200 ease-in-out;
}
.check-container.selected-card .checkmark path {
  @apply fill-success-70;
}
.check-container.selected-card .checkmark circle {
  @apply fill-gray-10 stroke-success-70 transition duration-200 ease-in-out;
}
.check-container.selected-card:hover .checkmark {
  @apply hover:scale-105 hover:border-[3px] hover:border-success-30;
}
.check-container.selected-card:hover .checkmark-box {
  @apply right-[-1px] top-[-1px];
}
.card-col .icon {
  @apply m-auto h-meas34 w-meas34 cursor-pointer;
}
.card-col .image-as-icon {
  @apply m-auto h-meas34 w-auto cursor-pointer;
}
.checkbox-title {
  @apply flex h-meas30 w-full flex-row items-start justify-start px-meas2 pt-meas6;
}
.checkbox-title > .icon-block .icon {
  @apply mt-[6px] h-[11.25px] w-[11.25px] xl:mt-meas0 xl:h-[18.75px] xl:w-[18.75px];
}


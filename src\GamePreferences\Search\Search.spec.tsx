import { render, screen, waitFor } from "@testing-library/react";
import Search from "./Search";
import React from "react";
import userEvent from "@testing-library/user-event";
import { axe } from "jest-axe";

describe("Search", () => {
  const mockOptions = [
    { value: "option1", label: "Option 1", image: "/option1.jpg" },
    { value: "option2", label: "Option 2", image: "/option2.jpg" },
    { value: "option3", label: "Option 3", image: "/option3.jpg" }
  ];

  const mockValue = { value: "option1", label: "Option 1", image: "/option1.jpg" };

  const defaultProps = {
    value: mockValue,
    options: mockOptions,
    onChange: jest.fn(),
    placeholder: "Search placeholder"
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should render search component correctly", () => {
    render(<Search {...defaultProps} />);

    expect(screen.getByRole("textbox")).toBeInTheDocument();
    expect(screen.getByPlaceholderText("Search placeholder")).toBeInTheDocument();
    expect(screen.getByDisplayValue("Option 1")).toBeInTheDocument();
  });

  it("should render with label when provided", () => {
    render(<Search {...defaultProps} label="Search Label" />);

    expect(screen.getByLabelText("Search Label")).toBeInTheDocument();
    expect(screen.getByText("Search Label")).toBeInTheDocument();
  });

  it("should show dropdown when input is clicked", async () => {
    render(<Search {...defaultProps} />);

    const input = screen.getByRole("textbox");
    await userEvent.click(input);

    expect(screen.getByRole("list")).toBeInTheDocument();
    expect(screen.getByText("Option 1")).toBeInTheDocument();
    expect(screen.getByText("Option 2")).toBeInTheDocument();
    expect(screen.getByText("Option 3")).toBeInTheDocument();
  });

  it("should filter options when typing", async () => {
    render(<Search {...defaultProps} />);

    const input = screen.getByRole("textbox");
    await userEvent.clear(input);
    await userEvent.type(input, "Option 2");

    expect(screen.getByRole("list")).toBeInTheDocument();
    expect(screen.getByText("Option 2")).toBeInTheDocument();
    expect(screen.queryByText("Option 1")).not.toBeInTheDocument();
    expect(screen.queryByText("Option 3")).not.toBeInTheDocument();
  });

  it("should filter options case insensitively", async () => {
    render(<Search {...defaultProps} />);

    const input = screen.getByRole("textbox");
    await userEvent.clear(input);
    await userEvent.type(input, "option 2");

    expect(screen.getByRole("list")).toBeInTheDocument();
    expect(screen.getByText("Option 2")).toBeInTheDocument();
    expect(screen.queryByText("Option 1")).not.toBeInTheDocument();
  });

  it("should call onChange when option is selected", async () => {
    const onChange = jest.fn();
    render(<Search {...defaultProps} onChange={onChange} />);

    const input = screen.getByRole("textbox");
    await userEvent.click(input);

    const option2Button = screen.getByText("Option 2");
    await userEvent.click(option2Button);

    expect(onChange).toHaveBeenCalledWith(mockOptions[1]);
  });

  it("should close dropdown when option is selected", async () => {
    render(<Search {...defaultProps} />);

    const input = screen.getByRole("textbox");
    await userEvent.click(input);

    const option2Button = screen.getByText("Option 2");
    await userEvent.click(option2Button);

    expect(screen.queryByRole("list")).not.toBeInTheDocument();
  });

  it("should update input value when option is selected", async () => {
    render(<Search {...defaultProps} />);

    const input = screen.getByRole("textbox");
    await userEvent.click(input);

    const option2Button = screen.getByText("Option 2");
    await userEvent.click(option2Button);

    expect(input).toHaveValue("Option 2");
  });

  it("should close dropdown on blur", async () => {
    render(<Search {...defaultProps} />);

    const input = screen.getByRole("textbox");
    await userEvent.click(input);

    expect(screen.getByRole("list")).toBeInTheDocument();

    await userEvent.tab();

    await waitFor(() => {
      expect(screen.queryByRole("list")).not.toBeInTheDocument();
    });
  });

  it("should handle disabled state", () => {
    render(<Search {...defaultProps} disabled />);

    const input = screen.getByRole("textbox");
    expect(input).toBeDisabled();
  });

  it("should not show dropdown when disabled", async () => {
    render(<Search {...defaultProps} disabled />);

    const input = screen.getByRole("textbox");
    await userEvent.click(input);

    expect(screen.queryByRole("list")).not.toBeInTheDocument();
  });

  it("should display error message when provided", () => {
    render(<Search {...defaultProps} errorMessage="Error occurred" />);

    expect(screen.getByText("Error occurred")).toBeInTheDocument();
    expect(screen.getByText("Error occurred")).toHaveClass("form-error-message");
  });

  it("should hide error message when dropdown is open", async () => {
    render(<Search {...defaultProps} errorMessage="Error occurred" />);

    const input = screen.getByRole("textbox");
    await userEvent.click(input);

    expect(screen.queryByText("Error occurred")).not.toBeInTheDocument();
  });

  it("should handle empty options array", () => {
    render(<Search {...defaultProps} options={[]} />);

    const input = screen.getByRole("textbox");
    expect(input).toBeInTheDocument();
  });

  it("should handle missing onChange prop gracefully", async () => {
    const propsWithoutOnChange = { ...defaultProps, onChange: undefined };
    
    expect(() => {
      render(<Search {...propsWithoutOnChange} />);
    }).not.toThrow();

    const input = screen.getByRole("textbox");
    await userEvent.click(input);
  });

  it("should show all options when dropdown opens after filtering", async () => {
    render(<Search {...defaultProps} />);

    const input = screen.getByRole("textbox");
    await userEvent.clear(input);
    await userEvent.type(input, "Option 2");

    expect(screen.getByText("Option 2")).toBeInTheDocument();
    expect(screen.queryByText("Option 1")).not.toBeInTheDocument();

    await userEvent.clear(input);
    await userEvent.click(input);

    expect(screen.getByText("Option 1")).toBeInTheDocument();
    expect(screen.getByText("Option 2")).toBeInTheDocument();
    expect(screen.getByText("Option 3")).toBeInTheDocument();
  });

  it("should handle string value prop", () => {
    render(<Search {...defaultProps} value={mockOptions[1]} />);

    const input = screen.getByRole("textbox");
    expect(input).toHaveValue("Option 2");
  });

  it("should handle object value prop", () => {
    render(<Search {...defaultProps} value={mockValue} />);

    const input = screen.getByRole("textbox");
    expect(input).toHaveValue("Option 1");
  });

  it("should highlight selected option in dropdown", async () => {
    render(<Search {...defaultProps} />);

    const input = screen.getByRole("textbox");
    await userEvent.click(input);

    const selectedOption = screen.getByText("Option 1").closest("button");
    expect(selectedOption).toHaveClass("selected");
  });

  it("should use aria-label when provided", () => {
    render(<Search {...defaultProps} ariaLabel="Custom aria label" />);

    const input = screen.getByRole("textbox");
    expect(input).toHaveAttribute("aria-label", "Custom aria label");
  });

  it("should fallback to label for aria-label", () => {
    render(<Search {...defaultProps} label="Search Label" />);

    const input = screen.getByRole("textbox");
    expect(input).toHaveAttribute("aria-label", "Search Label");
  });

  it("should fallback to placeholder for aria-label", () => {
    render(<Search {...defaultProps} />);

    const input = screen.getByRole("textbox");
    expect(input).toHaveAttribute("aria-label", "Search placeholder");
  });

  it("should be accessible", async () => {
    const { container } = render(<Search {...defaultProps} label="Search Label" />);

    expect(await axe(container)).toHaveNoViolations();
  });

  it("should handle partial text matches", async () => {
    render(<Search {...defaultProps} />);

    const input = screen.getByRole("textbox");
    await userEvent.clear(input);
    await userEvent.type(input, "1");

    expect(screen.getByText("Option 1")).toBeInTheDocument();
    expect(screen.queryByText("Option 2")).not.toBeInTheDocument();
    expect(screen.queryByText("Option 3")).not.toBeInTheDocument();
  });

  it("should restore selected item label on blur when no selection made", async () => {
    render(<Search {...defaultProps} />);

    const input = screen.getByRole("textbox");
    await userEvent.clear(input);
    await userEvent.type(input, "Some random text");
    await userEvent.tab();

    await waitFor(() => {
      expect(input).toHaveValue("Option 1");
    });
  });

  it("should forward ref correctly", () => {
    const ref = React.createRef<HTMLInputElement>();
    render(<Search ref={ref} {...defaultProps} />);

    expect(ref.current).toBeInstanceOf(HTMLInputElement);
    expect(ref.current).toBe(screen.getByRole("textbox"));
  });
});

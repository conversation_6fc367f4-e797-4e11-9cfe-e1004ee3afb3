import { render, screen, waitFor } from "@testing-library/react";
import LegalEntityForm from "./LegalEntityForm";
import React from "react";
import { renderWithToast } from "@src/Helpers/Toast";
import userEvent from "@testing-library/user-event";
import { FormProvider, useForm } from "react-hook-form";
import { axe } from "jest-axe";
import { aLegalEntityInformationPayload } from "@eait-playerexp-cn/creator-test-fixtures";
import { LegalEntityType, LegalInformationPayload } from "@eait-playerexp-cn/creator-types";
import { Country } from "@eait-playerexp-cn/metadata-types";
import { Buttons, InformationPageLabels } from "@src/Translations/InformationPageLabels";
import { aCountry } from "@eait-playerexp-cn/metadata-test-fixtures";
import Random from "@src/Factories/Random";

const successToast = jest.fn();
jest.mock("@eait-playerexp-cn/core-ui-kit", () => ({
  ...jest.requireActual("@eait-playerexp-cn/core-ui-kit"),
  useToast: () => ({
    success: successToast
  })
}));

const FormWrapper = ({
  children,
  defaultValues = {} as LegalInformationPayload
}: {
  children: React.ReactNode;
  defaultValues?: LegalInformationPayload;
}) => {
  const methods = useForm({
    defaultValues,
    mode: "onChange"
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
};

describe("LegalEntityForm", () => {
  const legalEntity: LegalInformationPayload = aLegalEntityInformationPayload();
  const countries: Country[] = [aCountry(), aCountry(), aCountry()];
  const infoLabels = InformationPageLabels.infoLabels;

  const props = {
    labels: { infoLabels, buttons: Buttons },
    legalEntity,
    countries,
    onChangeAsMailingAddress: jest.fn(),
    onChange: jest.fn(),
    isSaved: false,
    isLoader: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should show legal entity form correctly", () => {
    render(
      <FormWrapper>
        <LegalEntityForm {...props} />
      </FormWrapper>
    );

    expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
    expect(screen.getByText(infoLabels.legalEntityType)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.legalEntityDescription)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.entityType)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.street)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.city)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.country)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.state)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.zipCode)).toBeInTheDocument();
    expect(screen.getByText(props.legalEntity.street)).toBeInTheDocument();
    expect(screen.getByText(props.legalEntity.city)).toBeInTheDocument();
    expect(screen.getByText(props.legalEntity.country.name)).toBeInTheDocument();
    expect(screen.getByText(props.legalEntity.zipCode)).toBeInTheDocument();
  });

  it("should show business name when entity type is BUSINESS", () => {
    const businessLegalEntity = aLegalEntityInformationPayload({ entityType: "BUSINESS" });

    render(
      <FormWrapper>
        <LegalEntityForm {...props} legalEntity={businessLegalEntity} />
      </FormWrapper>
    );

    expect(screen.getByText(infoLabels.labels.businessName)).toBeInTheDocument();
    expect(screen.getByText(businessLegalEntity.businessName)).toBeInTheDocument();
  });

  it("should enable edit mode when edit button is clicked", async () => {
    render(
      <FormWrapper>
        <LegalEntityForm
          {...{
            ...props,
            legalEntity: { ...legalEntity, entityType: "INDIVIDUAL" as LegalEntityType },
            isSaved: undefined
          }}
        />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Save" })).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.individual)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.labels.business)).toBeInTheDocument();
    expect(screen.getAllByRole("textbox").length).toBe(4);
    expect(screen.getByTestId("legal-entity-country")).toBeInTheDocument();
    expect(screen.getByRole("checkbox")).toBeInTheDocument();
    expect(screen.getByText(props.labels.infoLabels.labels.legalAddressAsMailingAddress)).toBeInTheDocument();
    expect(props.onChange).toHaveBeenCalledTimes(1);
  });

  it("should exit edit mode when cancel button is clicked", async () => {
    render(
      <FormWrapper>
        <LegalEntityForm {...props} />
      </FormWrapper>
    );
    await userEvent.click(screen.getByRole("button", { name: "Edit" }));
    expect(screen.queryByRole("button", { name: "Edit" })).not.toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Save" })).toBeInTheDocument();

    await userEvent.click(screen.getByRole("button", { name: "Cancel" }));

    expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
    expect(screen.queryByRole("button", { name: "Cancel" })).not.toBeInTheDocument();
    expect(screen.queryByRole("button", { name: "Save" })).not.toBeInTheDocument();
  });

  it("should exit edit mode when isSaved becomes true", async () => {
    const { rerender } = render(
      <FormWrapper>
        <LegalEntityForm {...props} />
      </FormWrapper>
    );
    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();

    const propsWithSaved = { ...props, isSaved: true };
    rerender(
      <FormWrapper>
        <LegalEntityForm {...propsWithSaved} />
      </FormWrapper>
    );

    await waitFor(() => {
      expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
    });
  });

  it("shows radio buttons for entity type options in edit mode", async () => {
    render(
      <FormWrapper>
        <LegalEntityForm {...props} />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    expect(screen.getByText(infoLabels.labels.individual)).toHaveClass("radio-btn-label");
    expect(screen.getByText(infoLabels.labels.business)).toHaveClass("radio-btn-label");
  });

  it("should handle entity type selection change", async () => {
    render(
      <FormWrapper>
        <LegalEntityForm {...props} />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    const businessOption = screen.getByText(infoLabels.labels.business);
    await userEvent.click(businessOption);

    await waitFor(() => {
      const radioItem = businessOption.closest(".radio-btn-container").querySelector(".radio-btn-item");
      expect(radioItem).toHaveClass("selected");
    });
  });

  it("should call onChangeAsMailingAddress when checkbox is toggled", async () => {
    render(
      <FormWrapper>
        <LegalEntityForm {...props} />
      </FormWrapper>
    );
    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    await userEvent.click(screen.getByRole("checkbox"));

    expect(props.onChangeAsMailingAddress).toHaveBeenCalledTimes(1);
  });

  it("should show success toast when information is saved", async () => {
    const propsWithSaved = {
      ...props,
      isSaved: true
    };

    const { unmount } = renderWithToast(
      <FormWrapper>
        <LegalEntityForm {...propsWithSaved} />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    expect(successToast).toHaveBeenCalledWith(
      expect.objectContaining({
        props: expect.objectContaining({
          header: infoLabels.success.updatedInformationHeader,
          content: infoLabels.success.legalEntityType
        })
      }),
      expect.objectContaining({
        autoClose: expect.any(Number)
      })
    );

    unmount();
  });

  it("should be accessible", async () => {
    const { container } = render(
      <FormWrapper>
        <LegalEntityForm {...props} />
      </FormWrapper>
    );

    expect(await axe(container)).toHaveNoViolations();
  });

  it("should handle missing onChange prop", () => {
    const propsWithoutOnChange = {
      ...props,
      onChange: undefined
    };

    expect(() => {
      render(
        <FormWrapper>
          <LegalEntityForm {...propsWithoutOnChange} />
        </FormWrapper>
      );
    }).not.toThrow();
  });

  it("shows validation error messages", async () => {
    render(
      <FormWrapper>
        <LegalEntityForm
          {...{
            ...props,
            legalEntity: {
              ...legalEntity,
              entityType: "BUSINESS" as LegalEntityType,
              businessName: Random.companyName()
            }
          }}
        />
      </FormWrapper>
    );
    await userEvent.click(screen.getByRole("button", { name: "Edit" }));

    await userEvent.clear(screen.getByPlaceholderText(infoLabels.labels.street));
    await userEvent.clear(screen.getByPlaceholderText(infoLabels.labels.city));
    await userEvent.clear(screen.getByPlaceholderText(infoLabels.labels.state));
    await userEvent.clear(screen.getByPlaceholderText(infoLabels.labels.zipCode));
    await userEvent.clear(screen.getByPlaceholderText(infoLabels.labels.businessName));

    expect(screen.getByRole("button", { name: "Save" })).toBeDisabled();
    expect(screen.getByText(infoLabels.messages.street)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.messages.city)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.messages.state)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.messages.zipCode)).toBeInTheDocument();
    expect(screen.getByText(infoLabels.messages.businessName)).toBeInTheDocument();
  });
});
import { render, screen, waitFor } from "@testing-library/react";
import UpdateProfilePicture from "./UpdateProfilePicture";
import React from "react";
import userEvent from "@testing-library/user-event";
import { axe } from "jest-axe";
import { AuthenticatedUser } from "@src/utils/types";
import { ButtonLabels, ProfileLabels, ProfilePictureLabels } from "../../types";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

Object.defineProperty(window, "matchMedia", {
  writable: true,
  value: jest.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn()
  }))
});

Object.defineProperty(window.URL, "createObjectURL", {
  writable: true,
  value: jest.fn().mockImplementation(() => "mocked-object-url")
});

Object.defineProperty(window.URL, "revokeObjectURL", {
  writable: true,
  value: jest.fn()
});

jest.mock("../../../utils", () => ({
  SESSION_USER: "SessionUser"
}));

const updateAvatar = jest.fn().mockResolvedValue({ data: { avatar: "new-avatar-url" } });
jest.mock("@eait-playerexp-cn/creators-http-client", () => ({
  CreatorsService: jest.fn().mockImplementation(() => ({
    updateAvatar
  }))
}));

describe("UpdateProfilePicture", () => {
  const user: AuthenticatedUser = {
    analyticsId: "xNIEuLMZCvlw+7q2oZaBuG0cTdqHTa2JP3l2eCZ2Sjg=",
    avatar: "https://eait-playerexp-cn-creator-avatar-images.s3.amazonaws.com/default-avatar.png",
    creatorCode: "TestAFFILIATE20250",
    isFlagged: false,
    isPayable: true,
    needsMigration: false,
    programs: ["creator_network", "affiliate", "sims_creator_program"],
    status: "ACTIVE",
    tier: "CREATORNETWORK",
    type: "CREATOR",
    username: "245902"
  };

  const labels = {
    buttons: {
      edit: "Edit",
      cancel: "Cancel",
      save: "Save",
      close: "Close",
      browse: "Browse"
    } as ButtonLabels,
    profilePictureLabels: {
      title: "Change My Avatar",
      message: "Select an image from your computer (JPEG, PNG or GIF). Image should be square and less than 1MB.",
      termsAndConditionsFirst:
        "Respect the rights of others. Only upload images that you own or that you have written permission to freely distribute.",
      termsAndConditionsMiddle: "User Agreement",
      termsAndConditionsLast: "for more information.",
      avatarRequired: "Please select an image",
      avatarInvalid: "Please select valid image",
      avatarMoreThanLimit: "Image size should be less than 1MB"
    } as ProfilePictureLabels,
    profileLabels: {
      updateAvatar: "Update Avatar"
    } as ProfileLabels
  };

  const props = {
    isPlaceholderDefault: false,
    src: user.avatar,
    user,
    labels,
    stableDispatch: jest.fn(),
    creatorsClient: jest.fn() as unknown as TraceableHttpClient,
    defaultAvatarImage: "https://example.com/default-avatar.png",
    locale: "en-us"
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("shows profile picture section correctly", () => {
    render(<UpdateProfilePicture {...props} />);

    const avatarImage = screen.getByAltText("profile-card-avatar");
    expect(avatarImage).toBeInTheDocument();
    expect(avatarImage).toHaveAttribute("src", user.avatar);
    expect(screen.getByRole("button", { name: "Update Avatar" })).toBeInTheDocument();
  });

  it("should open modal when update avatar button is clicked", async () => {
    render(<UpdateProfilePicture {...props} />);

    await userEvent.click(screen.getByRole("button", { name: "Update Avatar" }));

    expect(screen.getByTestId("modal-content-container")).toBeInTheDocument();
    expect(screen.getByText(labels.profilePictureLabels.title)).toBeInTheDocument();
    expect(screen.getByText(labels.profilePictureLabels.message)).toBeInTheDocument();
  });

  it("shows file input in modal", async () => {
    render(<UpdateProfilePicture {...props} />);

    await userEvent.click(screen.getByRole("button", { name: "Update Avatar" }));

    const fileInput = screen.getByLabelText(/browse/i);
    expect(fileInput).toBeInTheDocument();
    expect(fileInput).toHaveAttribute("type", "file");
    expect(fileInput).toHaveAttribute("hidden");
    expect(fileInput).toHaveAttribute("id", "upload");
    expect(fileInput).toHaveAttribute("name", "upload");
  });

  it("shows terms and conditions text in modal", async () => {
    render(<UpdateProfilePicture {...props} />);

    const updateButton = screen.getByRole("button", { name: "Update Avatar" });
    await userEvent.click(updateButton);

    expect(screen.getByText(labels.profilePictureLabels.termsAndConditionsFirst, { exact: false })).toBeInTheDocument();
    expect(screen.getByText("User Agreement")).toBeInTheDocument();
    expect(screen.getByText(/for more information/)).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Save" })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Save" })).toBeDisabled();
  });

  it("should close modal when cancel button is clicked", async () => {
    render(<UpdateProfilePicture {...props} />);
    await userEvent.click(screen.getByRole("button", { name: "Update Avatar" }));

    await userEvent.click(screen.getByRole("button", { name: "Cancel" }));

    await waitFor(() => {
      expect(screen.queryByTestId("modal-content-container")).not.toBeInTheDocument();
    });
  });

  it("should close modal when close button is clicked", async () => {
    render(<UpdateProfilePicture {...props} />);

    await userEvent.click(screen.getByRole("button", { name: "Update Avatar" }));

    await userEvent.click(screen.getByRole("button", { name: /close/i }));

    await waitFor(() => {
      expect(screen.queryByTestId("modal-content-container")).not.toBeInTheDocument();
    });
  });

  it("should show default avatar when src is not provided", async () => {
    const propsWithoutSrc = {
      ...props,
      src: undefined
    };
    render(<UpdateProfilePicture {...propsWithoutSrc} />);

    await userEvent.click(screen.getByRole("button", { name: "Update Avatar" }));

    expect(screen.getByAltText("profile-card-avatar")).toBeInTheDocument();
    expect(screen.getByRole("img", { name: "Avatar Preview" })).toHaveAttribute("src", "/img/migrations/default.png");
  });

  it("handles default placeholder correctly", () => {
    const propsWithPlaceholder = {
      ...props,
      isPlaceholderDefault: undefined
    };

    render(<UpdateProfilePicture {...propsWithPlaceholder} />);

    expect(screen.getByAltText("profile-card-avatar")).toBeInTheDocument();
  });

  it("should be accessible", async () => {
    const { container } = render(<UpdateProfilePicture {...props} />);

    expect(await axe(container)).toHaveNoViolations();
  });

  it("should handle missing props gracefully", () => {
    const minimalProps = {
      ...props,
      src: ""
    };

    expect(() => {
      render(<UpdateProfilePicture {...minimalProps} />);
    }).not.toThrow();
  });

  it("shows error message when invalid image is selected", async () => {
    render(<UpdateProfilePicture {...{ ...props, locale: "ja-jp" }} />);

    await userEvent.click(screen.getByRole("button", { name: "Update Avatar" }));

    const fileInput = screen.getByLabelText(/browse/i);
    await userEvent.upload(fileInput, new File(["invalid-image"], "invalid-file.zip", { type: "compressed-zip" }));

    expect(screen.getByText(labels.profilePictureLabels.avatarInvalid)).toBeInTheDocument();
  });

  it("shows error message when file size is greater than 1 MB", async () => {
    render(<UpdateProfilePicture {...props} />);

    await userEvent.click(screen.getByRole("button", { name: "Update Avatar" }));

    const fileInput = screen.getByLabelText(/browse/i);
    await userEvent.upload(
      fileInput,
      new File([new Uint8Array(1024 * 1024 + 100)], "invalid-file.jpeg", { type: "image/jpeg" })
    );

    expect(screen.getByText(labels.profilePictureLabels.avatarMoreThanLimit)).toBeInTheDocument();
  });

  it("enables save button on valid image selection", async () => {
    render(<UpdateProfilePicture {...props} />);
    await userEvent.click(screen.getByRole("button", { name: "Update Avatar" }));

    const fileInput = screen.getByLabelText(/browse/i);
    await userEvent.upload(fileInput, new File(["valid-image"], "invalid-file.jpeg", { type: "image/jpeg" }));

    expect(screen.getByRole("button", { name: "Save" })).toBeEnabled();
  });

  it("handles avatar upload success correctly", async () => {
    render(<UpdateProfilePicture {...props} />);
    await userEvent.click(screen.getByRole("button", { name: "Update Avatar" }));
    const file = new File(["avatar"], "avatar.png", { type: "image/png" });
    const input = screen.getByLabelText(/browse/i);
    await userEvent.upload(input, file);

    await userEvent.click(screen.getByRole("button", { name: /save/i }));

    await waitFor(() => {
      expect(updateAvatar).toHaveBeenCalledTimes(1);
      expect(props.stableDispatch).toHaveBeenCalledWith({
        type: "SessionUser",
        data: expect.objectContaining({
          avatar: expect.stringContaining("?t=")
        })
      });
    });
  });

  it("handles 422 validation error in submitHandler", async () => {
    const error = {
      response: {
        status: 422,
        data: {
          errors: {
            avatar: "Avatar validation failed"
          }
        }
      }
    };
    updateAvatar.mockRejectedValueOnce(error);
    render(<UpdateProfilePicture {...props} />);
    await userEvent.click(screen.getByRole("button", { name: "Update Avatar" }));
    const fileInput = screen.getByLabelText(/browse/i);
    const testFile = new File(["test-image"], "test.jpg", { type: "image/jpeg" });
    await userEvent.upload(fileInput, testFile);
    const saveButton = screen.getByRole("button", { name: "Save" });

    await userEvent.click(saveButton);

    await waitFor(() => {
      expect(screen.getByText(error.response.data.errors.avatar)).toBeInTheDocument();
    });
  });

  it("handles general error in submitHandler", async () => {
    const error = {
      message: "Network error occurred"
    };
    updateAvatar.mockRejectedValueOnce(error);
    render(<UpdateProfilePicture {...props} />);
    await userEvent.click(screen.getByRole("button", { name: "Update Avatar" }));
    const fileInput = screen.getByLabelText(/browse/i);
    const testFile = new File(["test-image"], "test.jpg", { type: "image/jpeg" });
    await userEvent.upload(fileInput, testFile);
    const saveButton = screen.getByRole("button", { name: "Save" });

    await userEvent.click(saveButton);

    await waitFor(() => {
      expect(screen.getByText(error.message)).toBeInTheDocument();
    });
    expect(screen.getByTestId("modal-content-container")).toBeInTheDocument();
  });
});
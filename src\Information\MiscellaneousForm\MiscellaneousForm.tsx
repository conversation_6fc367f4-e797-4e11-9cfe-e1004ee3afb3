import { Controller, useFormContext } from "react-hook-form";
import React, { memo, useCallback, useEffect, useMemo, useState } from "react";
import { Select } from "@eait-playerexp-cn/core-ui-kit";
import ProfileFormButtons from "../ProfileFormButtons/ProfileFormButtons";
import { MultiSelect } from "@eait-playerexp-cn/core-ui-kit";
import { Toast, useToast, FormTitle } from "@eait-playerexp-cn/core-ui-kit";
import { ButtonLabels, InfoLabels } from "../types";
import { HardwarePartner } from "@eait-playerexp-cn/metadata-types";
import { AdditionalInformationPayload } from "@eait-playerexp-cn/creator-types";

const MiscellaneousFormRules = (labels: InfoLabels) => {
  return {
    tShirtSize: {
      required: labels.messages.tShirtSize
    }
  };
};

interface MiscellaneousFormProps {
  labels: {
    infoLabels: InfoLabels;
    buttons: ButtonLabels;
  };
  additionalInformation: AdditionalInformationPayload;
  hardwarePartners: HardwarePartner[];
  onChange: () => void;
  isSaved: boolean;
  isLoader: boolean;
}

const tShirtSizes = [
  {
    value: "XS",
    label: "XS"
  },
  {
    value: "S",
    label: "S"
  },
  {
    value: "M",
    label: "M"
  },
  {
    value: "L",
    label: "L"
  },
  {
    value: "XL",
    label: "XL"
  },
  {
    value: "XXL",
    label: "XXL"
  },
  {
    value: "XXXL",
    label: "XXXL"
  }
];

const MiscellaneousForm = memo(function MiscellaneousForm({
  labels,
  additionalInformation,
  hardwarePartners,
  onChange,
  isSaved = false,
  isLoader
}: MiscellaneousFormProps) {
  const { infoLabels, buttons } = labels;
  const rules = useMemo(() => MiscellaneousFormRules(infoLabels), [infoLabels]);
  const { control } = useFormContext();
  const [isEdit, setIsEdit] = useState(false);
  const { success: successToast } = useToast();
  const timetoDisplay = Math.min(Math.max(infoLabels.success.miscellaneous.length * 50, 2000), 7000);
  const [defaultHoodieSize, setDefaultHoodieSize] = useState({ label: "", value: "" });
  const [defaultHardwarePartners, setDefaultHardwarePartners] = useState([]);

  useEffect(() => {
    setDefaultHoodieSize({
      value: additionalInformation.hoodieSize || "",
      label: additionalInformation.hoodieSize || "None"
    });

    const convertedHardwarePartners =
      additionalInformation.hardwarePartners?.map((hardwarepartner) => ({
        value: hardwarepartner.id,
        label: hardwarepartner.name
      })) || [];
    setDefaultHardwarePartners(convertedHardwarePartners);
  }, [additionalInformation]);

  const onEditChange = useCallback(
    (isChecked: boolean) => {
      setIsEdit(isChecked);
      if (onChange) onChange();
    },
    [isEdit]
  );

  useEffect(() => {
    if (isEdit && isSaved) setIsEdit(false);
  }, [isSaved]);

  return (
    <>
      {isSaved &&
        isEdit &&
        successToast(
          <Toast header={infoLabels.success.updatedInformationHeader} content={infoLabels.success.miscellaneous} />,
          {
            autoClose: timetoDisplay
          }
        )}
      <div className="miscellaneous-information">
        <div className="form-sub-title-and-action">
          <FormTitle subTitle={infoLabels.miscellaneous} />
          <ProfileFormButtons {...{ buttons, action: onEditChange, isSaved, isLoader }} />
        </div>

        <div className="miscellaneous-field-title" id="hoodie-size">
          {infoLabels.labels.tShirtSize}
        </div>
        <div className="miscellaneous-field">
          {!isEdit && (additionalInformation.hoodieSize || infoLabels.labels.none)}
          {isEdit && (
            <Controller
              control={control}
              name="hoodieSize"
              rules={rules.tShirtSize}
              defaultValue={additionalInformation.hoodieSize}
              render={({ field, fieldState: { error } }) => (
                <Select
                  id="hoodie-size" // label prop is not passed, so 'id' is being used for aria-labelledby
                  selectedOption={defaultHoodieSize}
                  errorMessage={error && error.message}
                  options={tShirtSizes}
                  onChange={(item) => {
                    field.onChange(item);
                  }}
                />
              )}
            />
          )}
        </div>

        <div className="miscellaneous-field-title">{infoLabels.labels.hardwarePartners}</div>
        <div className={`miscellaneous-field ${!isEdit && "hardware-partner-field"}`}>
          {!isEdit &&
            (additionalInformation.hardwarePartners?.length
              ? additionalInformation.hardwarePartners.map((item, key) => (
                  <div className="hardware-partner-head" key={key}>
                    {item.name}
                  </div>
                ))
              : infoLabels.labels.none)}
          {isEdit && (
            <Controller
              control={control}
              name="hardwarePartners"
              defaultValue={defaultHardwarePartners}
              render={({ field, fieldState: { error } }) => (
                <MultiSelect
                  selectedOptions={field.value || defaultHardwarePartners}
                  errorMessage={(error && error.message) || ""}
                  {...field}
                  options={hardwarePartners}
                  placeholder={infoLabels.labels.hardwarePartners}
                />
              )}
            />
          )}
        </div>
      </div>
    </>
  );
});
export default MiscellaneousForm;

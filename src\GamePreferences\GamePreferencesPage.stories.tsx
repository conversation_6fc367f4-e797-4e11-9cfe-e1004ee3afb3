import React, { ComponentType, ReactElement } from "react";
import { <PERSON>a, StoryObj } from "@storybook/react";
import GamePreferencesPage from "./GamePreferencesPage";
import { FranchisesYouPlayLabels } from "@src/Translations/FranchisesYouPlayLabels";
import { InformationPageLabels, Buttons } from "@src/Translations/InformationPageLabels";
import { aCommunicationPreferencesPayload, aConnectedAccountResponse, aCreatorCodeResponse, aLegalEntityInformationPayload, aMailingAddressPayload, anAdditionalInformationPayload, aPreferredFranchiseResponse, aPreferredPlatformResponse, aProgramRequest } from "@eait-playerexp-cn/creator-test-fixtures";
import { aFranchise, aPlatform } from "@eait-playerexp-cn/metadata-test-fixtures";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { CreatorProfile } from "@eait-playerexp-cn/creators-http-client";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

const meta: Meta<typeof GamePreferencesPage> = {
  title: "Game Preferences Page",
  component: GamePreferencesPage,
  parameters: {
    layout: "fullscreen"
  },
  decorators: [
    (Story: ComponentType): ReactElement => (
      <div className="storybook-dark">
        <Story />
      </div>
    )
  ]
};

export default meta;

type Story = StoryObj<typeof GamePreferencesPage>;

export const GamePreferencesPageStory: Story = {
  args: {
    labels : {
        franchisesYouPlayLabels: FranchisesYouPlayLabels,
        infoLabels: InformationPageLabels.infoLabels,
        buttons: Buttons,
    },
    creator: {
      preferredPrimaryFranchise: { value: "fifa", label: "FIFA" },
      preferredSecondaryFranchises: [{ value: "madden", label: "Madden NFL" }],
      preferredPrimaryPlatform: { value: "playstation", label: "PlayStation" },
      preferredSecondaryPlatforms: [{ value: "xbox", label: "Xbox" }],
      id: "001VB00000DUe5PYAT",
      creatorTypes: ["YOUTUBER"],
      accountInformation: {
        defaultGamerTag: "245902",
        nucleusId: *************,
        firstName: "Mouli",
        lastName: "Nrusimhadri",
        originEmail: "<EMAIL>",
        dateOfBirth: {
          millisecondsEpoch: ************,
          format: () => "July 30, 1995",
          toDate: () => new Date(************)
        } as unknown as LocalizedDate,
        needsMigration: false,
        payable: true,
        flagged: false,
        disabled: false,
        preferredName: "Mouli",
        preferredPronouns: null
      },
      communicationPreferences: aCommunicationPreferencesPayload(),
      legalInformation: aLegalEntityInformationPayload(),
      mailingAddress: aMailingAddressPayload(),
      connectedAccounts: aConnectedAccountResponse(),
      additionalInformation: anAdditionalInformationPayload(),
      socialLinks: [],
      creatorCode: aCreatorCodeResponse(),
      joinedPrograms: ["creator_network", "affiliate", "sims_creator_program"],
      program: aProgramRequest(),
      formattedRegistrationDate: () => ""
    } as unknown as CreatorProfile,
    updateCreator: () => {},
    franchises: [
      { value: "fifa", label: "FIFA", image: "/img/franchises-you-play/fifa.png" },
      { value: "madden", label: "Madden NFL", image: "/img/franchises-you-play/madden.jpg"   }
    ],
    platforms: [
      { value: "playstation", label: "PlayStation", imageAsIcon: "/img/platforms/playstation.png" },
      { value: "xbox", label: "Xbox", imageAsIcon: "/img/platforms/xbox.png"   }
    ],
    onUpdatedPrimaryFranchise: () => {},
    onUpdatedSecondaryFranchises: () => {},
    onUpdatedPrimaryPlatformInProfile: () => {},
    onUpdatedSecondaryPlatformsInProfile: () => {},
    configuration:{
      program: "test-program",
      defaultAvatarImage: "/default-avatar.png",
      creatorsClient: {} as unknown as TraceableHttpClient
    },
    state: {
      isValidationError: false,
      validationErrors: [],
      isError: false
    },
    stableDispatch: () => {},
    errorHandler: () => {},
    locale: "en-us"
  }
};

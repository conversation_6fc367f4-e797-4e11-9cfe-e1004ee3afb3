import { render, screen, waitFor, within } from "@testing-library/react";
import GamePreferencesPage from "./GamePreferencesPage";
import React from "react";
import userEvent from "@testing-library/user-event";
import { axe } from "jest-axe";
import { CreatorProfile } from "@eait-playerexp-cn/creators-http-client";
import { Franchise, Platform } from "@eait-playerexp-cn/metadata-types";
import { Buttons, InformationPageLabels } from "@src/Translations/InformationPageLabels";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { aFranchise, aPlatform } from "@eait-playerexp-cn/metadata-test-fixtures";
import { FranchisesYouPlayLabels } from "@src/Translations/FranchisesYouPlayLabels";
import { renderWithToast, triggerAnimationEnd } from "@src/Helpers/Toast";
import { ERROR, onToastClose, VALIDATION_ERROR } from "@src/utils";

const updateCreator = jest.fn().mockResolvedValue({});
const creatorsServiceInstance = { updateCreator };

jest.mock("@eait-playerexp-cn/creators-http-client", () => {
  return {
    ...jest.requireActual("@eait-playerexp-cn/creators-http-client"),
    CreatorsService: jest.fn().mockImplementation(() => creatorsServiceInstance)
  };
});
jest.mock("@src/utils", () => ({
  ...jest.requireActual("@src/utils"),
  onToastClose: jest.fn()
}));

describe("GamePreferencesPage", () => {
  const franchises: Franchise[] = [
    aFranchise({ value: "fifa", label: "FIFA", image: "/img/franchises/fifa.png" }),
    aFranchise({ value: "madden", label: "Madden NFL", image: "/img/franchises/madden.png" })];
  const platforms: Platform[] = [ 
    aPlatform({ value: "PlayStation", label: "PlayStation", imageAsIcon: "/img/platforms/playstation.png" }),
    aPlatform({ value: "xbox", label: "Xbox", imageAsIcon: "/img/platforms/xbox.png"})];
  const creator: CreatorProfile = {
    preferredPrimaryFranchise: {
      value: "fifa",
      label: "FIFA",
      image: "/img/franchises/fifa.png"
    },
    preferredSecondaryFranchises: [
      {
        value: "madden",
        label: "Madden NFL",
        image: "/img/franchises/madden.png"
      }
    ],
    preferredPrimaryPlatform: {
      value: "PlayStation",
      label: "PlayStation",
      imageAsIcon: "/img/platforms/playstation.png"
    },
    preferredSecondaryPlatforms: [
      {
        value: "xbox",
        label: "Xbox",
        imageAsIcon: "/img/platforms/xbox.png"
      }
    ],
    updatedPrimaryFranchise: jest.fn().mockReturnValue(true),
    updatedSecondaryFranchises: jest.fn().mockReturnValue(true),
    updatedPrimaryPlatform: jest.fn().mockReturnValue(true),
    updatedSecondaryPlatforms: jest.fn().mockReturnValue(true)
  } as unknown as CreatorProfile;
  const franchisesYouPlayLabels = FranchisesYouPlayLabels;
  const infoLabels = InformationPageLabels.infoLabels;
  const props = {
    labels: { 
      franchisesYouPlayLabels, 
      infoLabels, 
      buttons: Buttons
    },
    creator,
    updateCreator: jest.fn(),
    franchises,
    platforms,
    onUpdatedPrimaryFranchise: jest.fn(),
    onUpdatedSecondaryFranchises: jest.fn(),
    onUpdatedPrimaryPlatformInProfile: jest.fn(),
    onUpdatedSecondaryPlatformsInProfile: jest.fn(),
    configuration: {
      defaultAvatarImage: "/default-avatar.png",
      program: "test-program",
      creatorsClient: {} as unknown as TraceableHttpClient
    },
    stableDispatch: jest.fn(),
    state: {
      isValidationError: false,
      validationErrors: [],
      isError: false
    },
    locale: "en-us",
    errorHandler: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should show game preferences page correctly", () => {
    render(<GamePreferencesPage {...props} />);

    expect(screen.getByText(props.labels.franchisesYouPlayLabels.title)).toBeInTheDocument();
    expect(screen.getByText(props.labels.franchisesYouPlayLabels.description)).toBeInTheDocument();
    expect(screen.getByText(props.labels.franchisesYouPlayLabels.primaryFranchiseTitle)).toBeInTheDocument();
    expect(screen.getByText(props.labels.franchisesYouPlayLabels.primaryFranchiseSubTitle)).toBeInTheDocument();
    expect(screen.getByText(props.labels.franchisesYouPlayLabels.secondaryFranchiseTitle)).toBeInTheDocument();
    expect(screen.getByText(props.labels.franchisesYouPlayLabels.secondaryFranchiseSubTitle)).toBeInTheDocument();
    expect(screen.getByText(props.labels.infoLabels.platformPreferences)).toBeInTheDocument();
    expect(screen.getByText(props.labels.infoLabels.primaryPlatform)).toBeInTheDocument();
    expect(screen.getByText(props.labels.infoLabels.platformPreferencesTitle)).toBeInTheDocument();
    expect(screen.getByText(props.labels.infoLabels.secondaryPlatform)).toBeInTheDocument();
    expect(screen.getByText(props.labels.infoLabels.secondaryPlatformTitle)).toBeInTheDocument();
    expect(screen.getByText("FIFA")).toBeInTheDocument();
    expect(screen.getAllByRole("button", { name: props.labels.buttons.edit })).toHaveLength(2);
  });

  it("should handle empty creator preferences", () => {
    const emptyCreator: CreatorProfile = {
      preferredPrimaryFranchise: null,
      preferredSecondaryFranchises: [],
      preferredPrimaryPlatform: null,
      preferredSecondaryPlatforms: []
    } as CreatorProfile;

    const propsWithEmptyCreator = {
      ...props,
      creator: emptyCreator
    };

    render(<GamePreferencesPage {...propsWithEmptyCreator} />);

    expect(screen.getByText(props.labels.franchisesYouPlayLabels.title)).toBeInTheDocument();
    expect(screen.getByText(props.labels.infoLabels.platformPreferences)).toBeInTheDocument();
  });

  it("should handle empty franchises and platforms arrays", () => {
    const propsWithEmptyArrays = {
      ...props,
      franchises: [],
      platforms: []
    };

    expect(() => {
      render(<GamePreferencesPage {...propsWithEmptyArrays} />);
    }).not.toThrow();
  });

  it("should enable edit mode when franchise edit button is clicked", async () => {
    render(<GamePreferencesPage {...props} />);
    const editButtons = screen.getAllByRole("button", { name: props.labels.buttons.edit });
    const franchiseEditButton = editButtons[0];
    
    await userEvent.click(franchiseEditButton);

    expect(screen.getByRole("button", { name: props.labels.buttons.save })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: props.labels.buttons.cancel })).toBeInTheDocument();
  });

  it("should enable edit mode when platform edit button is clicked", async () => {
    render(<GamePreferencesPage {...props} />);
    const editButtons = screen.getAllByRole("button", { name: props.labels.buttons.edit });
    const platformEditButton = editButtons[1];

    await userEvent.click(platformEditButton);

    expect(screen.getByRole("button", { name: props.labels.buttons.save })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: props.labels.buttons.cancel })).toBeInTheDocument();
  });

  it("should call update functions when franchises are changed", async () => {
    renderWithToast(<GamePreferencesPage {...props} />);
    await userEvent.click(screen.getAllByRole("button", { name: props.labels.buttons.edit })[0]);    
    await userEvent.click(screen.getByPlaceholderText("primaryFranchise"));
    await userEvent.click(screen.getAllByText("Madden NFL")[0]);
    await userEvent.click(screen.getByTestId("multi-select-id"));
    await userEvent.click(screen.getAllByText("FIFA")[0]);

    await userEvent.click(screen.getByRole("button", { name: props.labels.buttons.save }));
    
    expect(props.updateCreator).toHaveBeenCalled()
    expect(props.onUpdatedPrimaryFranchise).toHaveBeenCalled();
    expect(props.onUpdatedSecondaryFranchises).toHaveBeenCalled();
  });

  it("should call update functions when platforms are changed", async () => {
    renderWithToast(<GamePreferencesPage {...props} />);
    await userEvent.click(screen.getAllByRole("button", { name: props.labels.buttons.edit })[1]);
    await userEvent.click(screen.getByTestId("primary-platform"));
    await userEvent.click(screen.getAllByText("Xbox")[0]);
    await userEvent.click(screen.getByTestId("PlayStation"));

    await userEvent.click(screen.getByRole("button", { name: props.labels.buttons.save }));
    
    expect(props.onUpdatedPrimaryPlatformInProfile).toHaveBeenCalled();
    expect(props.onUpdatedSecondaryPlatformsInProfile).toHaveBeenCalled();
  });

  it("shows error toast", async () => {
    const propsWithError = {
      ...props,
      state: { ...props.state, isError: true, isValidationError: true }
    };
    const { unmount } = renderWithToast(<GamePreferencesPage {...propsWithError} />);
    
    await waitFor(() => {
      expect(screen.getByText(props.labels.infoLabels.layout.main.unhandledError)).toBeInTheDocument();
    });
    unmount();
  })

  it("dispatches error on toast close", async () => {
    jest.useFakeTimers();
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
    const propsWithError = {
      ...props,
      state: { ...props.state, isError: true, isValidationError: false }
    };
    const { unmount } = renderWithToast(<GamePreferencesPage {...propsWithError} />);
    const { getByRole } = within(await screen.findByRole("alert"));
    expect(getByRole("heading")).toHaveTextContent(props.labels.infoLabels.layout.main.unhandledError);
    expect(getByRole("button", { name: /close/i })).toBeInTheDocument();

    await user.click(screen.getByRole("button", { name: /Close/i }));

    triggerAnimationEnd(screen.getByText(props.labels.infoLabels.layout.main.unhandledError));
    await waitFor(() => {
      expect(onToastClose).toHaveBeenCalledTimes(1);
      expect(onToastClose).toHaveBeenCalledWith(ERROR, props.stableDispatch);
    });
    unmount();
    jest.useRealTimers();
  });

  it("dispatches validation error on toast close", async () => {
    jest.useFakeTimers();
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
    const propsWithError = {
      ...props,
      state: { ...props.state, isError: false, isValidationError: true }
    };
    const { unmount } = renderWithToast(<GamePreferencesPage {...propsWithError} />);
    const { getByRole } = within(await screen.findByRole("alert"));
    expect(getByRole("heading")).toHaveTextContent(props.labels.infoLabels.layout.main.unhandledError);
    expect(getByRole("button", { name: /close/i })).toBeInTheDocument();

    await user.click(screen.getByRole("button", { name: /Close/i }));

    triggerAnimationEnd(screen.getByText(props.labels.infoLabels.layout.main.unhandledError));
    await waitFor(() => {
      expect(onToastClose).toHaveBeenCalledTimes(1);
      expect(onToastClose).toHaveBeenCalledWith(VALIDATION_ERROR, props.stableDispatch);
    });
    unmount();
    jest.useRealTimers();
  });

  it("should be accessible", async () => {
    let results: any;
    const { container } = render(<GamePreferencesPage {...props} />);

    results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
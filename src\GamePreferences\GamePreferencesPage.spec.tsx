import { render, screen } from "@testing-library/react";
import GamePreferencesPage from "./GamePreferencesPage";
import React from "react";
import userEvent from "@testing-library/user-event";
import { axe } from "jest-axe";
import { CreatorProfile } from "@eait-playerexp-cn/creators-http-client";
import { Franchise, Platform } from "@eait-playerexp-cn/metadata-types";
import { Buttons, InformationPageLabels } from "@src/Translations/InformationPageLabels";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { aFranchise, aPlatform } from "@eait-playerexp-cn/metadata-test-fixtures";
import { FranchisesYouPlayLabels } from "@src/Translations/FranchisesYouPlayLabels";

const successToast = jest.fn();
jest.mock("@eait-playerexp-cn/core-ui-kit", () => ({
  ...jest.requireActual("@eait-playerexp-cn/core-ui-kit"),
  useToast: () => ({
    success: successToast
  })
}));

jest.mock("@src/utils", () => ({
  ERROR: "ERROR",
  VALIDATION_ERROR: "VALIDATION_ERROR",
  useAsync: () => ({
    execute: jest.fn(),
    status: "idle",
    value: null,
    error: null
  }),
  onToastClose: jest.fn(),
  toastContent: jest.fn()
}));

jest.mock("next/router", () => ({
  useRouter: () => ({
    push: jest.fn(),
    query: {}
  })
}));

describe("GamePreferencesPage", () => {
  const franchises: Franchise[] = [
    aFranchise({ value: "fifa", label: "FIFA", image: "/img/franchises/fifa.png" }),
    aFranchise({ value: "madden", label: "Madden NFL", image: "/img/franchises/madden.png" })];

  const platforms: Platform[] = [ 
    aPlatform({ value: "pc", label: "PC", imageAsIcon: "/img/platforms/pc.png" }),
    aPlatform({ value: "xbox", label: "Xbox", imageAsIcon: "/img/platforms/xbox.png"})];

  const creator: CreatorProfile = {
    preferredPrimaryFranchise: {
      value: "fifa",
      label: "FIFA",
      image: "/img/franchises/fifa.png"
    },
    preferredSecondaryFranchises: [
      {
        value: "madden",
        label: "Madden NFL",
        image: "/img/franchises/madden.png"
      }
    ],
    preferredPrimaryPlatform: {
      value: "pc",
      label: "PC",
      imageAsIcon: "/img/platforms/pc.png"
    },
    preferredSecondaryPlatforms: [
      {
        value: "xbox",
        label: "Xbox",
        imageAsIcon: "/img/platforms/xbox.png"
      }
    ]
  } as unknown as CreatorProfile;

  const franchisesYouPlayLabels = FranchisesYouPlayLabels;
  const infoLabels = InformationPageLabels.infoLabels;
  const props = {
    labels: { 
      franchisesYouPlayLabels, 
      infoLabels, 
      buttons: Buttons
    },
    creator,
    updateCreator: jest.fn(),
    franchises,
    platforms,
    layout: {
      main: {
        unhandledError: "An error occurred"
      }
    },
    onUpdatedPrimaryFranchise: jest.fn(),
    onUpdatedSecondaryFranchises: jest.fn(),
    onUpdatedPrimaryPlatformInProfile: jest.fn(),
    onUpdatedSecondaryPlatformsInProfile: jest.fn(),
    configuration: {
      defaultAvatarImage: "/default-avatar.png",
      program: "test-program",
      creatorsClient: {} as unknown as TraceableHttpClient
    },
    stableDispatch: jest.fn(),
    state: {
      isValidationError: false,
      validationErrors: [],
      isError: false
    },
    locale: "en-us",
    errorHandler: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should show game preferences page correctly", () => {
    render(<GamePreferencesPage {...props} />);

    expect(screen.getByText(props.labels.franchisesYouPlayLabels.title)).toBeInTheDocument();
    expect(screen.getByText(props.labels.franchisesYouPlayLabels.description)).toBeInTheDocument();
    expect(screen.getByText(props.labels.franchisesYouPlayLabels.primaryFranchiseTitle)).toBeInTheDocument();
    expect(screen.getByText(props.labels.franchisesYouPlayLabels.primaryFranchiseSubTitle)).toBeInTheDocument();
    expect(screen.getByText(props.labels.franchisesYouPlayLabels.secondaryFranchiseTitle)).toBeInTheDocument();
    expect(screen.getByText(props.labels.franchisesYouPlayLabels.secondaryFranchiseSubTitle)).toBeInTheDocument();
    expect(screen.getByText(props.labels.infoLabels.platformPreferences)).toBeInTheDocument();
    expect(screen.getByText(props.labels.infoLabels.primaryPlatform)).toBeInTheDocument();
    expect(screen.getByText(props.labels.infoLabels.platformPreferencesTitle)).toBeInTheDocument();
    expect(screen.getByText(props.labels.infoLabels.secondaryPlatforms)).toBeInTheDocument();
    expect(screen.getByText(props.labels.infoLabels.secondaryPlatformsTitle)).toBeInTheDocument();
    expect(screen.getByText("FIFA")).toBeInTheDocument();
  });

  it("should show edit buttons for both forms", () => {
    render(<GamePreferencesPage {...props} />);

    const editButtons = screen.getAllByRole("button", { name: props.labels.buttons.edit });
    expect(editButtons).toHaveLength(2);
  });

  it("should handle empty creator preferences", () => {
    const emptyCreator: CreatorProfile = {
      preferredPrimaryFranchise: null,
      preferredSecondaryFranchises: [],
      preferredPrimaryPlatform: null,
      preferredSecondaryPlatforms: []
    } as CreatorProfile;

    const propsWithEmptyCreator = {
      ...props,
      creator: emptyCreator
    };

    render(<GamePreferencesPage {...propsWithEmptyCreator} />);

    expect(screen.getByText(props.labels.franchisesYouPlayLabels.title)).toBeInTheDocument();
    expect(screen.getByText(props.labels.infoLabels.platformPreferences)).toBeInTheDocument();
  });

  it("should handle empty franchises and platforms arrays", () => {
    const propsWithEmptyArrays = {
      ...props,
      franchises: [],
      platforms: []
    };

    expect(() => {
      render(<GamePreferencesPage {...propsWithEmptyArrays} />);
    }).not.toThrow();
  });

  it("should be accessible", async () => {
    let results: any;
    const { container } = render(<GamePreferencesPage {...props} />);

    results = await axe(container);

    expect(results).toHaveNoViolations();
  });

  it("should enable edit mode when franchise edit button is clicked", async () => {
    render(<GamePreferencesPage {...props} />);

    const editButtons = screen.getAllByRole("button", { name: props.labels.buttons.edit });
    const franchiseEditButton = editButtons[0];
    
    await userEvent.click(franchiseEditButton);

    expect(screen.getByRole("button", { name: props.labels.buttons.save })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: props.labels.buttons.cancel })).toBeInTheDocument();
  });

  it("should enable edit mode when platform edit button is clicked", async () => {
    render(<GamePreferencesPage {...props} />);

    const editButtons = screen.getAllByRole("button", { name: props.labels.buttons.edit });
    const platformEditButton = editButtons[1];
    
    await userEvent.click(platformEditButton);

    expect(screen.getByRole("button", { name: props.labels.buttons.save })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: props.labels.buttons.cancel })).toBeInTheDocument();
  });
});

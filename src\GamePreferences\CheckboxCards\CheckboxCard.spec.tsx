import { render, screen } from "@testing-library/react";
import CheckboxCard from "./CheckboxCard";
import React from "react";
import userEvent from "@testing-library/user-event";
import { axe } from "jest-axe";
import { CheckboxCardItem } from "./CheckboxCards";

const mockIcon = () => <svg data-testid="mock-icon" />;

describe("CheckboxCard", () => {
  const basicItem: CheckboxCardItem = {
    value: "test-value",
    label: "Test Label",
    checked: false
  };

  const itemWithImage: CheckboxCardItem = {
    value: "image-value",
    label: "Image Label",
    image: "/test-image.jpg",
    checked: false
  };

  const itemWithIcon: CheckboxCardItem = {
    value: "icon-value",
    label: "Icon Label",
    icon: mockIcon,
    checked: false
  };

  const itemWithImageAsIcon: CheckboxCardItem = {
    value: "image-as-icon-value",
    label: "Image As Icon Label",
    imageAsIcon: "/test-icon.png",
    checked: false
  };

  const checkedItem: CheckboxCardItem = {
    value: "checked-value",
    label: "Checked Label",
    checked: true
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should render checkbox card with basic item correctly", () => {
    render(<CheckboxCard item={basicItem} />);

    expect(screen.getByLabelText("Test Label")).toBeInTheDocument();
    expect(screen.getByText("Test Label")).toBeInTheDocument();
    expect(screen.getByRole("checkbox")).not.toBeChecked();
  });

  it("should render checkbox card with image background", () => {
    render(<CheckboxCard item={itemWithImage} />);

    const checkmark = document.querySelector(".checkmark");
    expect(checkmark).toHaveStyle("background-image: url(/test-image.jpg)");
    expect(screen.getByText("Image Label")).toBeInTheDocument();
  });

  it("should render checkbox card with icon", () => {
    render(<CheckboxCard item={itemWithIcon} />);

    expect(screen.getByTestId("mock-icon")).toBeInTheDocument();
    expect(screen.getByText("Icon Label")).toBeInTheDocument();
  });

  it("should render checkbox card with image as icon", () => {
    render(<CheckboxCard item={itemWithImageAsIcon} />);

    const imageAsIcon = screen.getByRole("img");
    expect(imageAsIcon).toHaveAttribute("src", "/test-icon.png");
    expect(imageAsIcon).toHaveClass("image-as-icon");
    expect(screen.getByText("Image As Icon Label")).toBeInTheDocument();
  });

  it("should render checked checkbox card with selected styling", () => {
    render(<CheckboxCard item={checkedItem} />);

    expect(screen.getByRole("checkbox")).toBeChecked();
    expect(document.querySelector(".check-container")).toHaveClass("selected-card");
    expect(document.querySelector(".checkmark-layout")).toHaveClass("selected-card");
  });

  it("should call onChange when checkbox is clicked", async () => {
    const onChange = jest.fn();
    render(<CheckboxCard item={basicItem} onChange={onChange} />);

    const checkbox = screen.getByRole("checkbox");
    await userEvent.click(checkbox);

    expect(onChange).toHaveBeenCalledWith(basicItem, true);
  });

  it("should call onChange with false when unchecking", async () => {
    const onChange = jest.fn();
    render(<CheckboxCard item={checkedItem} onChange={onChange} />);

    const checkbox = screen.getByRole("checkbox");
    await userEvent.click(checkbox);

    expect(onChange).toHaveBeenCalledWith(checkedItem, false);
  });

  it("should not call onChange when disabled", async () => {
    const onChange = jest.fn();
    render(<CheckboxCard item={basicItem} onChange={onChange} disabled />);

    const checkbox = screen.getByRole("checkbox");
    expect(checkbox).toBeDisabled();
    
    await userEvent.click(checkbox);
    expect(onChange).not.toHaveBeenCalled();
  });

  it("should not call onChange when readOnly", async () => {
    const onChange = jest.fn();
    render(<CheckboxCard item={basicItem} onChange={onChange} readOnly />);

    const checkbox = screen.getByRole("checkbox");
    await userEvent.click(checkbox);

    expect(onChange).not.toHaveBeenCalled();
  });

  it("should apply disabled styling when disabled", () => {
    render(<CheckboxCard item={basicItem} disabled />);

    expect(document.querySelector(".card-col")).toHaveAttribute("data-disabled", "true");
    expect(document.querySelector(".checkmark")).toHaveAttribute("data-disabled", "true");
  });

  it("should handle missing onChange prop gracefully", async () => {
    expect(() => {
      render(<CheckboxCard item={basicItem} />);
    }).not.toThrow();

    const checkbox = screen.getByRole("checkbox");
    await userEvent.click(checkbox);
  });

  it("should handle empty or null image gracefully", () => {
    const itemWithEmptyImage: CheckboxCardItem = {
      value: "empty-image",
      label: "Empty Image",
      image: "",
      checked: false
    };

    render(<CheckboxCard item={itemWithEmptyImage} />);

    const checkmark = document.querySelector(".checkmark");
    expect(checkmark).not.toHaveStyle("background-image: url()");
  });

  it("should handle null image gracefully", () => {
    const itemWithNullImage: CheckboxCardItem = {
      value: "null-image",
      label: "Null Image",
      image: null,
      checked: false
    };

    render(<CheckboxCard item={itemWithNullImage} />);

    expect(screen.getByText("Null Image")).toBeInTheDocument();
  });

  it("should update selected class when checked state changes", () => {
    const { rerender } = render(<CheckboxCard item={basicItem} />);

    expect(document.querySelector(".check-container")).not.toHaveClass("selected-card");

    rerender(<CheckboxCard item={{ ...basicItem, checked: true }} />);

    expect(document.querySelector(".check-container")).toHaveClass("selected-card");
  });

  it("should not update selected class when disabled", () => {
    render(<CheckboxCard item={checkedItem} disabled />);

    expect(document.querySelector(".check-container")).not.toHaveClass("selected-card");
  });

  it("should have proper accessibility attributes", () => {
    render(<CheckboxCard item={basicItem} />);

    const checkbox = screen.getByRole("checkbox");
    expect(checkbox).toHaveAttribute("aria-label", "Test Label");
    expect(checkbox).toHaveAttribute("data-testid", "Test Label");
  });

  it("should be accessible", async () => {
    const { container } = render(<CheckboxCard item={basicItem} />);

    expect(await axe(container)).toHaveNoViolations();
  });

  it("should render SVG checkmark correctly", () => {
    render(<CheckboxCard item={basicItem} />);

    const svg = document.querySelector(".checkmark-box");
    expect(svg).toBeInTheDocument();
    expect(svg).toHaveAttribute("width", "64");
    expect(svg).toHaveAttribute("height", "64");
  });

  it("should handle complex item with all properties", () => {
    const complexItem: CheckboxCardItem = {
      value: "complex-value",
      label: "Complex Label",
      image: "/background.jpg",
      imageAsIcon: "/icon.png",
      icon: mockIcon,
      checked: true
    };

    render(<CheckboxCard item={complexItem} />);

    expect(screen.getByText("Complex Label")).toBeInTheDocument();
    expect(screen.getByRole("checkbox")).toBeChecked();
    expect(screen.getByTestId("mock-icon")).toBeInTheDocument();
    expect(screen.getByRole("img")).toHaveAttribute("src", "/icon.png");
  });

  it("should forward ref correctly", () => {
    const ref = React.createRef<HTMLInputElement>();
    render(<CheckboxCard ref={ref} item={basicItem} />);

    expect(ref.current).toBeInstanceOf(HTMLInputElement);
    expect(ref.current).toBe(screen.getByRole("checkbox"));
  });
});

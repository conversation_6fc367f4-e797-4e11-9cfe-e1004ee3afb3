import { Icon } from "@eait-playerexp-cn/core-ui-kit";
import React from "react";
import { forwardRef, useCallback, useEffect, useState } from "react";
import { CheckboxCardItem } from "./CheckboxCards";

export interface CheckboxCardProps {
  disabled?: boolean;
  onChange?: (item: CheckboxCardItem, isChecked: boolean) => void;
  item: CheckboxCardItem;
  readOnly?: boolean;
}

const CheckboxCard = forwardRef<HTMLInputElement, CheckboxCardProps>(
  ({ disabled, onChange, item, readOnly }, ref) => {
    const { label, image, checked, icon, imageAsIcon } = item;
    const styles: React.CSSProperties = image === undefined || image === null || image === "" ? {} : { backgroundImage: `url(${image})` };
    const [selectedClass, setSelectedClass] = useState<string>("");

    useEffect(() => {
      !disabled && setSelectedClass(checked ? " selected-card" : "");
    }, [disabled, checked]);

    const cardOnClick = useCallback(
      (isChecked: boolean) => {
        setSelectedClass(isChecked ? " selected-card" : "");
        onChange && onChange(item, isChecked);
      },
      [onChange, item]
    );

    return (
      <div className="card-col" data-disabled={disabled} onChange={(e: React.ChangeEvent<HTMLInputElement>) => !readOnly && cardOnClick(e.target.checked)}>
        <label className={`check-container${selectedClass}`}>
          <input
            ref={ref}
            data-testid={label}
            aria-label={label}
            type="checkbox"
            defaultChecked={checked}
            disabled={disabled}
          />{" "}
          <span className={`checkmark-layout${selectedClass}`}>&nbsp;</span>
          <span className="checkmark" style={styles} data-disabled={disabled}>
            {icon && <Icon icon={icon} />}
            {imageAsIcon && <img alt="" src={`${imageAsIcon}`} className="image-as-icon" />}
            <svg
              width="64"
              height="64"
              viewBox="0 0 64 64"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="checkmark-box"
            >
              <path d="M64 64L0 0H64V64Z" fill="currentFillColor" />
              <circle cx="46" cy="19" r="13.25" fill="currentColor" stroke="currentStrokeColor" strokeWidth="1.5" />
            </svg>
          </span>
        </label>
        <div className="checkbox-title">
          <span className="card-title">{label}</span>
        </div>
      </div>
    );
  }
);

CheckboxCard.displayName = 'CheckboxCard';
export default CheckboxCard;

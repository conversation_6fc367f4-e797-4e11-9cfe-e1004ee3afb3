import { Controller, useFormContext } from "react-hook-form";
import React, { useCallback, useEffect, useState } from "react";
import ProfileFormButtons from "../../Information/ProfileFormButtons/ProfileFormButtons";
import { MultiSelect } from "@eait-playerexp-cn/core-ui-kit";
// import FormTitle from "../../Information/FormTitle/FormTitle";
import { Toast, useToast } from "@eait-playerexp-cn/core-ui-kit";
import { ButtonLabels } from "@src/Information/types";
import { Franchise } from "@eait-playerexp-cn/metadata-types";
import { FranchisesYouPlayLabels } from "../GamePreferencesPage";

type SecFranchiseCardProps = {
  franchise: Franchise;
};
export interface FranchiseYouPlayFormProps {
    labels: {
        franchisesYouPlayLabels: FranchisesYouPlayLabels;
        buttons: ButtonLabels;
    };
    onChange?: () => void;
    isSaved?: boolean;
    franchises: Franchise[];
    preferredPrimaryFranchises?: Franchise | null;
    preferredSecondaryFranchises: Franchise[];
    isLoader?: boolean;
}

const FranchiseYouPlayForm: React.FC<FranchiseYouPlayFormProps> = ({
    labels,
    onChange,
    isSaved = false,
    franchises,
    preferredPrimaryFranchises,
    preferredSecondaryFranchises,
    isLoader
}) => {
  const { franchisesYouPlayLabels, buttons } = labels; 
  const { control } = useFormContext();
  const [isEdit, setIsEdit] = useState<boolean>(false);
  const [primaryFranchiseImage, setPrimaryFranchiseImage] = useState<string | undefined>(
    preferredPrimaryFranchises && preferredPrimaryFranchises.image
  );
  const { success: successToast } = useToast();
  const modalSuccessText = franchisesYouPlayLabels.messages.success.content;
  const timeToDisplay = Math.min(Math.max(modalSuccessText.length * 50, 2000), 7000);
  const [secondaryFranchiseSelection, setSecondaryFranchiseSelection] = useState<Franchise[]>(preferredSecondaryFranchises);

  const onEditChange = useCallback(
    (isChecked: boolean) => {
      setIsEdit(isChecked);
      onChange && onChange();
    },
    [onChange]
  );

  useEffect(() => {
    isEdit && isSaved && setIsEdit(false);
  }, [isSaved]);

  return (
    <>
      {isEdit &&
        isSaved &&
        successToast(
          <Toast
            header={franchisesYouPlayLabels.messages.success.header}
            content={modalSuccessText}
          />,
          {
            autoClose: { timetoDisplay: timeToDisplay }
          }
        )}
      <div className="profile-game-preferences-title-actions">
        <FormTitle title={franchisesYouPlayLabels.title} />
        <ProfileFormButtons {...{ buttons, action: onEditChange, isSaved, isLoader }} />
      </div>
      <div className="profile-game-preferences-description">{franchisesYouPlayLabels.description}</div>
      <div className="profile-game-preferences-primary-container">
        <h4 className="profile-game-preferences-primary-title">{franchisesYouPlayLabels.primaryFranchiseTitle}</h4>
        <div className="profile-game-preferences-primary-subtitle">
          {franchisesYouPlayLabels.primaryFranchiseSubTitle}
        </div>
        <div className="profile-game-preferences-primary-show">
          {!isEdit && preferredPrimaryFranchises && (
            <>
              <div className="profile-game-preferences-tag">{preferredPrimaryFranchises.label}</div>
              <div className="profile-game-preferences-option">
                <img
                  alt="Franchise image"
                  className="profile-game-preferences-option-image"
                  src={`${preferredPrimaryFranchises.image}`}
                />
              </div>
            </>
          )}
          {isEdit && (
            <Controller
              control={control}
              name="primaryFranchise"
              defaultValue={preferredPrimaryFranchises}
              render={({ field, fieldState: { error } }) => (
                <>
                  <select
                    {...field}
                    onChange={(e) => {
                      const selectedFranchise = franchises.find(f => f.value === e.target.value);
                      if (selectedFranchise) {
                        setPrimaryFranchiseImage(selectedFranchise.image);
                        field.onChange(selectedFranchise);
                      }
                    }}
                    value={field.value?.value || ""}
                  >
                    <option value="">{franchisesYouPlayLabels.labels.primaryFranchise}</option>
                    {franchises.map((franchise) => (
                      <option key={franchise.value} value={franchise.value}>
                        {franchise.label}
                      </option>
                    ))}
                  </select>
                  {error && <div className="error-message">{error.message}</div>}
                  <div className="profile-game-preferences-option">
                    {primaryFranchiseImage && (
                      <img
                        alt="Franchise image"
                        className="profile-game-preferences-option-image"
                        src={`${primaryFranchiseImage}`}
                      />
                    )}
                  </div>
                </>
              )}
            />
          )}
        </div>
      </div>
      <div className="profile-game-preferences-secondary-container">
        <h4 className="profile-game-preferences-secondary-title">{franchisesYouPlayLabels.secondaryFranchiseTitle}</h4>
        <div className="profile-game-preferences-secondary-subtitle">
          {franchisesYouPlayLabels.secondaryFranchiseSubTitle}
        </div>
        <div className="profile-secondary-game-preferences-show">
          {!isEdit && (
            <>
              <div className="profile-secondary-game-preferences-tags">
                {preferredSecondaryFranchises.map((item, index) => (
                  <div className="profile-game-preferences-tag" key={`secondary-franchise-label-${index}`}>
                    {item.label}
                  </div>
                ))}
              </div>
              <div className="profile-secondary-game-preferences-options">
                {preferredSecondaryFranchises.map((item, index) => (
                  <SecFranchiseCard key={`secondary-franchise-card-${index}`} franchise={item} />
                ))}
              </div>
            </>
          )}
          {isEdit && (
            <Controller
              control={control}
              name="secondaryFranchise"
              defaultValue={preferredSecondaryFranchises}
              render={({ field, fieldState: { error } }) => (
                <>
                  <MultiSelect
                    {...field}
                    selectedOptions={field.value}
                    options={franchises}
                    errorMessage={(error && error.message) || ""}
                    placeholder={franchisesYouPlayLabels.secondaryFranchiseTitle}
                    onChange={(selectedData: Franchise[]) => {
                      setSecondaryFranchiseSelection(selectedData);
                      field.onChange(selectedData);
                    }}
                  />
                  <div className="profile-secondary-game-preferences-options">
                    {secondaryFranchiseSelection.map((item, index) => (
                      <SecFranchiseCard key={`secondary-franchise-card-${index}`} franchise={item} />
                    ))}
                  </div>
                </>
              )}
            />
          )}
        </div>
      </div>
    </>
  );
};
export default FranchiseYouPlayForm;

const SecFranchiseCard: React.FC<SecFranchiseCardProps> = ({ franchise }) => {
  return (
    <div className="profile-secondary-franchise-option">
      <img
        alt="Franchise image"
        className="profile-secondary-franchise-option-image"
        src={franchise.image ? `${franchise.image}` : `/img/franchises-you-play/FIFA.png`}
      />
      <div className="profile-secondary-franchise-option-text">{franchise.label}</div>
    </div>
  );
};

import React, { useCallback, useEffect, useMemo, useState } from "react";
import FranchiseYouPlayForm from "./FranchiseYouPlayForm/FranchiseYouPlayForm";
import PlatformPreferencesForm from "./PlatformPreferencesForm/PlatformPreferencesForm";
import { CreatorProfile, CreatorsService } from "@eait-playerexp-cn/creators-http-client";
import { Toast, useToast } from "@eait-playerexp-cn/core-ui-kit";
import { ERROR, onToastClose, toastContent, useAsync, VALIDATION_ERROR } from "@src/utils";
import { Dispatch, ErrorHandling, Layout, State } from "@src/utils/types";
import {  UpdateCreatorRequest } from "@eait-playerexp-cn/creator-types";
import { ButtonLabels, InfoLabels } from "@src/Information/types";
import { Franchise, Platform } from "@eait-playerexp-cn/metadata-types";
import { Configuration } from "@src/Information/InformationPage";
import Form from "../utils/Form";

export type FranchisesYouPlayLabels = {
      title: string;
      primaryFranchiseTitle: string;
      primaryFranchiseSubTitle: string;
      secondaryFranchiseTitle: string;
      secondaryFranchiseSubTitle: string;
      description: string;
      messages: {
        primaryFranchise: string;
        success: {
          header: string;
          content: string;
        }
      },
      labels: {
        primaryFranchise: string;
        loadMore: string;
      }
};
type UpdatePrimaryFranchise = ({locale, creator}:{locale: string, creator: CreatorProfile}) => void; 
type UpdatedSecondaryPlatformsInProfile =  ({locale, creator, selectedPlatforms}: { locale: string; creator: CreatorProfile; selectedPlatforms: Platform}) => void;

export interface GamePreferencesPageProps {
    labels: {
        franchisesYouPlayLabels: FranchisesYouPlayLabels;
        infoLabels: InfoLabels;
        buttons: ButtonLabels;
    },
  creator: CreatorProfile;
  updateCreator: (creator: UpdateCreatorRequest) => void;
  franchises: Franchise[];
  platforms: Platform[];
  configuration: Configuration;
  stableDispatch: Dispatch;
  state: State;
  errorHandler: ErrorHandling;
  locale: string,
  onUpdatedPrimaryFranchise: UpdatePrimaryFranchise; // This is callback for analytics to be implemented in shell app
    onUpdatedSecondaryFranchises: UpdatePrimaryFranchise;
    onUpdatedPrimaryPlatformInProfile: UpdatePrimaryFranchise;
    onUpdatedSecondaryPlatformsInProfile: UpdatedSecondaryPlatformsInProfile;
}

const GamePreferences: React.FC<GamePreferencesPageProps> = ({
  labels,
  creator,
  updateCreator,
  franchises,
  platforms,
  configuration,
  stableDispatch,
  errorHandler,
  state,
  locale,
  onUpdatedPrimaryFranchise,
  onUpdatedSecondaryFranchises,
  onUpdatedPrimaryPlatformInProfile,
  onUpdatedSecondaryPlatformsInProfile
}) => {
    const { infoLabels, buttons, franchisesYouPlayLabels } = labels;
  const { main: { unhandledError } } = infoLabels.layout;
  const { error: errorToast } = useToast();
  const { creatorsClient, defaultAvatarImage, program } = configuration;
  const {isValidationError, validationErrors, isError}  = state;
  const [isFranchiseSaved, setIsFranchiseSaved] = useState<boolean>(false);
  const [isPlatformSaved, setIsPlatformSaved] = useState<boolean>(false);
  const [preferredPrimaryFranchises, setPreferredPrimaryFranchises] = useState<Franchise | null>(null);
  const [preferredPrimaryPlatforms, setPreferredPrimaryPlatforms] = useState<Platform | null>(null);
  const [preferredSecondaryFranchises, setPreferredSecondaryFranchises] = useState<Franchise[] | null>(null);
  const [preferredSecondaryPlatforms, setPreferredSecondaryPlatforms] = useState<Platform[] | null>(null);
  const creatorService = useMemo(() => new CreatorsService(creatorsClient, defaultAvatarImage), [
    creatorsClient
  ]);

  useEffect(() => {
    if (creator && franchises) {
      let preferredPrimaryFranchises = creator.preferredPrimaryFranchise as unknown as Franchise;
      franchises.forEach((item) => {
        if (preferredPrimaryFranchises && item.value === preferredPrimaryFranchises.value)
          preferredPrimaryFranchises = { ...preferredPrimaryFranchises, image: item.image};
      });
      setPreferredPrimaryFranchises(preferredPrimaryFranchises);
      const preferredSecondaryFranchises = creator.preferredSecondaryFranchises as unknown as Franchise[];
      preferredSecondaryFranchises.forEach((item) => {
        franchises.forEach((obj) => {
          if (item.value === obj.value) item = { ...item, image: obj.image};
        });
      });
      setPreferredSecondaryFranchises(preferredSecondaryFranchises);
    }
    if (creator && platforms) {
      let preferredPrimaryPlatforms = creator.preferredPrimaryPlatform as unknown as Platform;
      platforms.forEach((item) => {
        if (preferredPrimaryPlatforms && item.value === preferredPrimaryPlatforms.value)
          preferredPrimaryPlatforms = { ...preferredPrimaryPlatforms, imageAsIcon: item.imageAsIcon};
      });
      setPreferredPrimaryPlatforms(preferredPrimaryPlatforms);
      setPreferredSecondaryPlatforms(creator.preferredSecondaryPlatforms as unknown as Platform[]);
    }
  }, [creator, franchises, platforms]);

  const submitFranchise = useCallback(
    async (data) => {
      try {
        const primaryFranchiseValue = { id: data.primaryFranchise.value, type: "PRIMARY" };
        const values = data.secondaryFranchise.map((secondaryFranchise) => ({
          id: secondaryFranchise.value,
          type: "SECONDARY"
        }));
        values.push(primaryFranchiseValue);
        await creatorService.updateCreator({
          preferredFranchises: values,
          program: { code: program }
        } as unknown as UpdateCreatorRequest);

        const preferredPrimaryFranchise = data.primaryFranchise;
        // TODO: this is sometimes null, while on live reload
        franchises?.forEach((franchise) => {
          if (franchise.value === preferredPrimaryFranchise.value) {
            preferredPrimaryFranchise.value = franchise.value;
            preferredPrimaryFranchise.image = franchise.image;
            preferredPrimaryFranchise.label = franchise.label;
            preferredPrimaryFranchise.value = franchise.value;
          }
        });
        setPreferredPrimaryFranchises(preferredPrimaryFranchise);
        const preferredSecondaryFranchises = data.secondaryFranchise;
        preferredSecondaryFranchises.forEach((item) => {
          franchises?.forEach((franchise) => {
            if (item.id === franchise.value) {
              item.value = franchise.value;
              item.image = franchise.image;
              item.label = franchise.label;
            }
          });
        });
        setPreferredSecondaryFranchises(preferredSecondaryFranchises);
        if (creator.updatedPrimaryFranchise(preferredPrimaryFranchise.label)) {
          creator = { ...creator, preferredPrimaryFranchise: preferredPrimaryFranchise} as unknown as CreatorProfile;
          onUpdatedPrimaryFranchise({ locale, creator });
        }
        if (creator.updatedSecondaryFranchises(preferredSecondaryFranchises.map((franchise) => franchise.label))) {
          creator = { ...creator,preferredSecondaryFranchises: preferredSecondaryFranchises} as unknown as CreatorProfile;
          onUpdatedSecondaryFranchises({ locale, creator });
        }
        updateCreator(creator as unknown as UpdateCreatorRequest);
        setIsFranchiseSaved(true);
      } catch (e) {
        errorHandler(stableDispatch, e);
      }
    },
    [stableDispatch, franchises]
  );

  const submitPlatform = useCallback(
    async (data) => {
      try {
        const primaryPlatformValue = { id: data.primaryPlatform.value, type: "PRIMARY" };
        const values = data.secondaryPlatform.map((secondaryPlatform) => {
          return { id: secondaryPlatform.value, type: "SECONDARY" };
        });
        values.push(primaryPlatformValue);

        await creatorService.updateCreator({
          preferredPlatforms: values,
          program: { code: program }
        } as unknown as UpdateCreatorRequest);

        const preferredPrimaryPlatforms = data.primaryPlatform;
        platforms.forEach((item) => {
          if (item.value === preferredPrimaryPlatforms.value) {
            preferredPrimaryPlatforms.label = item.label;
            preferredPrimaryPlatforms.value = item.value;
            preferredPrimaryPlatforms.imageAsIcon = item.imageAsIcon;
          }
        });
        setPreferredPrimaryPlatforms(preferredPrimaryPlatforms);
        const preferredSecondaryPlatformsResponse = data.secondaryPlatform;
        preferredSecondaryPlatformsResponse.forEach((item) => {
          platforms.forEach((obj) => {
            if (item.id === obj.value) {
              item.imageAsIcon = obj.imageAsIcon;
              item.label = obj.label;
              item.value = obj.value;
            }
          });
        });
        setPreferredSecondaryPlatforms(preferredSecondaryPlatformsResponse);
        if (creator.updatedPrimaryPlatform(preferredPrimaryPlatforms.label)) {
          creator = { ...creator, preferredPrimaryPlatform : preferredPrimaryPlatforms} as unknown as CreatorProfile;
          onUpdatedPrimaryPlatformInProfile({ locale, creator })
        }
        if (creator.updatedSecondaryPlatforms(preferredSecondaryPlatformsResponse.map((platform) => platform.label))) {
          onUpdatedSecondaryPlatformsInProfile({
            locale,
            creator,
            selectedPlatforms: preferredSecondaryPlatformsResponse
          });
          creator={...creator,preferredSecondaryPlatforms: preferredSecondaryPlatformsResponse} as unknown as CreatorProfile;
        }
        updateCreator(creator as unknown as UpdateCreatorRequest);
        setIsPlatformSaved(!isPlatformSaved);
      } catch (e) {
        errorHandler(stableDispatch, e);
      }
    },
    [stableDispatch, platforms]
  );

  const franchiseOnChange = useCallback(() => setIsFranchiseSaved(false), []);
  const platformOnChange = useCallback(() => setIsPlatformSaved(false), []);
  const { pending: pendingFranchiseUpd, execute: onSubmitFranchise } = useAsync(submitFranchise, false);
  const { pending: pendingPlatformUpd, execute: onSubmitPlatform } = useAsync(submitPlatform, false);

  useEffect(() => {
    if (isError || isValidationError) {
      errorToast(
        <Toast
          header={unhandledError}
          content={isError ? isError : toastContent(validationErrors)}
        />,
        {
          onClose: () => onToastClose(isError ? ERROR : VALIDATION_ERROR, stableDispatch)
        }
      );
    }
  }, [isError, isValidationError, stableDispatch, unhandledError]);

  return (
    <div className="profile-game-preferences">
      {preferredSecondaryFranchises && (
        <Form key="franchise" mode="onChange" onSubmit={onSubmitFranchise}>
          <FranchiseYouPlayForm
            {...{
                labels: { franchisesYouPlayLabels, buttons},
              franchises,
              preferredPrimaryFranchises,
              preferredSecondaryFranchises,
              onChange: franchiseOnChange,
              isSaved: isFranchiseSaved,
              isLoader: pendingFranchiseUpd
            }}
          />
        </Form>
      )}
      {preferredSecondaryPlatforms && (
        <Form key="platform" mode="onChange" onSubmit={onSubmitPlatform}>
          <PlatformPreferencesForm
            {...{
              labels : { infoLabels, buttons},
              platforms,
              preferredPrimaryPlatforms,
              preferredSecondaryPlatforms,
              onChange: platformOnChange,
              isSaved: isPlatformSaved,
              isLoader: pendingPlatformUpd
            }}
          />
        </Form>
      )}
    </div>
  );
};
export default GamePreferences;

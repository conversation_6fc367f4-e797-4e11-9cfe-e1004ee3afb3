import { act, render, screen, waitFor } from "@testing-library/react";
import PlatformPreferencesForm from "./PlatformPreferencesForm";
import React from "react";
import { renderWithToast } from "@src/Helpers/Toast";
import userEvent from "@testing-library/user-event";
import { FormProvider, useForm } from "react-hook-form";
import { axe } from "jest-axe";
import { Platform } from "@eait-playerexp-cn/metadata-types";
import { Buttons, InformationPageLabels } from "@src/Translations/InformationPageLabels";

const successToast = jest.fn();
jest.mock("@eait-playerexp-cn/core-ui-kit", () => ({
  ...jest.requireActual("@eait-playerexp-cn/core-ui-kit"),
  useToast: () => ({
    success: successToast
  })
}));



const FormWrapper = ({
  children,
  defaultValues = {}
}: {
  children: React.ReactNode;
  defaultValues?: any;
}) => {
  const methods = useForm({
    defaultValues,
    mode: "onChange"
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
};

describe("PlatformPreferencesForm", () => {
  const platforms: Platform[] = [
    {
      value: "pc",
      label: "PC",
      imageAsIcon: "/img/platforms/pc.png"
    },
    {
      value: "xbox",
      label: "Xbox",
      imageAsIcon: "/img/platforms/xbox.png"
    },
    {
      value: "playstation",
      label: "PlayStation",
      imageAsIcon: "/img/platforms/playstation.png"
    }
  ];

  const preferredPrimaryPlatforms: Platform = {
    value: "pc",
    label: "PC",
    imageAsIcon: "/img/platforms/pc.png"
  };

  const preferredSecondaryPlatforms: Platform[] = [
    {
      value: "xbox",
      label: "Xbox",
      imageAsIcon: "/img/platforms/xbox.png"
    }
  ];

  const infoLabels = InformationPageLabels.infoLabels;
  const props = {
    labels: {infoLabels, buttons: Buttons},
    platforms,
    preferredPrimaryPlatforms,
    preferredSecondaryPlatforms,
    onChange: jest.fn(),
    isSaved: false,
    isLoader: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should show platform preferences form correctly", () => {
    render(
      <FormWrapper>
        <PlatformPreferencesForm {...props} />
      </FormWrapper>
    );

    expect(screen.getByText(props.labels.infoLabels.platformPreferences)).toBeInTheDocument();
    expect(screen.getByText(props.labels.infoLabels.primaryPlatform)).toBeInTheDocument();
    expect(screen.getByText(props.labels.infoLabels.platformPreferencesTitle)).toBeInTheDocument();
    expect(screen.getByText(props.labels.infoLabels.secondaryPlatform)).toBeInTheDocument();
    expect(screen.getByText(props.labels.infoLabels.secondaryPlatformTitle)).toBeInTheDocument();
    expect(screen.getByRole("img", { name: /Primary platform label image/i})).toBeInTheDocument();
  });

  it("should display primary platform in read-only mode initially", () => {
    render(
      <FormWrapper>
        <PlatformPreferencesForm {...props} />
      </FormWrapper>
    );

    expect(screen.getByRole("button", { name: props.labels.buttons.edit })).toBeInTheDocument();
  });

  it("should enable edit mode when edit button is clicked", async () => {
    render(
      <FormWrapper>
        <PlatformPreferencesForm {...props} />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: props.labels.buttons.edit }));

    expect(screen.getByRole("button", { name: props.labels.buttons.cancel })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: props.labels.buttons.save })).toBeInTheDocument();
  });

  it("should show platform select in edit mode", async () => {
    render(
      <FormWrapper>
        <PlatformPreferencesForm {...props} />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: props.labels.buttons.edit }));

    expect(screen.getByRole("button", { name: "PC" })).toBeInTheDocument();
  });

  it("should call onChange when edit mode is toggled", async () => {
    render(
      <FormWrapper>
        <PlatformPreferencesForm {...props} />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: props.labels.buttons.edit }));

    expect(props.onChange).toHaveBeenCalledTimes(1);
  });

  it("should exit edit mode when cancel button is clicked", async () => {
    render(
      <FormWrapper>
        <PlatformPreferencesForm {...props} />
      </FormWrapper>
    );
    await userEvent.click(screen.getByRole("button", { name: props.labels.buttons.edit }));

    await userEvent.click(screen.getByRole("button", { name: props.labels.buttons.cancel }));

    expect(screen.getByRole("button", { name: props.labels.buttons.edit })).toBeInTheDocument();
    expect(screen.queryByRole("button", { name: props.labels.buttons.cancel })).not.toBeInTheDocument();
    expect(screen.queryByRole("button", { name: props.labels.buttons.save })).not.toBeInTheDocument();
  });

  it("should exit edit mode when isSaved becomes true", async () => {
    const { rerender } = render(
      <FormWrapper>
        <PlatformPreferencesForm {...props} />
      </FormWrapper>
    );
    await userEvent.click(screen.getByRole("button", { name: props.labels.buttons.edit }));
    expect(screen.getByRole("button", { name: props.labels.buttons.cancel })).toBeInTheDocument();

    const propsWithSaved = { ...props, isSaved: true };
    rerender(
      <FormWrapper>
        <PlatformPreferencesForm {...propsWithSaved} />
      </FormWrapper>
    );

    await waitFor(() => {
      expect(screen.getByRole("button", { name: props.labels.buttons.edit })).toBeInTheDocument();
    });
  });

  it("should handle platform selection change", async () => {
    render(
      <FormWrapper>
        <PlatformPreferencesForm {...props} />
      </FormWrapper>
    );
    await userEvent.click(screen.getByRole("button", { name: props.labels.buttons.edit }));
    const platformSelect = screen.getByRole("button", { name: "PC" });
    expect(platformSelect).toBeInTheDocument();

    await userEvent.click(platformSelect);

    const selectOptions = screen.getAllByText(/PC|Xbox|PlayStation/);
    expect(selectOptions.length).toBeGreaterThan(3); 
  });

  it("should show success toast when information is saved", async () => {
    const propsWithSaved = {
      ...props,
      isSaved: true
    };

    const { unmount } = renderWithToast(
      <FormWrapper>
        <PlatformPreferencesForm {...propsWithSaved} />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: props.labels.buttons.edit }));

    expect(successToast).toHaveBeenCalledWith(
      expect.objectContaining({
        props: expect.objectContaining({
          header: infoLabels.success.updatedInformationHeader,
          content: infoLabels.success.platformPreferences
        })
      }),
      expect.objectContaining({
        autoClose: expect.any(Number)
      })
    );

    unmount();
  });

  it("should handle missing onChange prop", () => {
    const propsWithoutOnChange = {
      ...props,
      onChange: undefined
    };

    expect(() => {
      render(
        <FormWrapper>
          <PlatformPreferencesForm {...propsWithoutOnChange} />
        </FormWrapper>
      );
    }).not.toThrow();
  });

  it("should handle save button click", async () => {
    render(
      <FormWrapper>
        <PlatformPreferencesForm {...props} />
      </FormWrapper>
    );
    await userEvent.click(screen.getByRole("button", { name: props.labels.buttons.edit }));

    await userEvent.click(screen.getByRole("button", { name: props.labels.buttons.save }));

    expect(props.onChange).toHaveBeenCalled();
  });

  it("should handle keyboard navigation", async () => {
    render(
      <FormWrapper>
        <PlatformPreferencesForm {...props} />
      </FormWrapper>
    );

    const editButton = screen.getByRole("button", { name: props.labels.buttons.edit });
    editButton.focus();

    await userEvent.keyboard("{Enter}");

    expect(screen.getByRole("button", { name: props.labels.buttons.save })).toBeInTheDocument();
  });

  it("should handle missing platform preferences", () => {
    const propsWithoutPreferences = {
      ...props,
      creator: {
        preferredPrimaryPlatform: null,
        preferredSecondaryPlatforms: null
      }
    };

    render(
      <FormWrapper>
        <PlatformPreferencesForm {...propsWithoutPreferences} />
      </FormWrapper>
    );

    expect(screen.getByText(props.labels.infoLabels.platformPreferences)).toBeInTheDocument();
  });

  it("should be accessible", async () => {
    let results;
    const { container } = render(
      <FormWrapper>
        <PlatformPreferencesForm {...props} />
      </FormWrapper>
    );

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });
});

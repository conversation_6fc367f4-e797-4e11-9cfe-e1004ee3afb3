import { act, render, screen, waitFor } from "@testing-library/react";
import PlatformPreferencesForm from "./PlatformPreferencesForm";
import React from "react";
import { renderWithToast } from "@src/Helpers/Toast";
import userEvent from "@testing-library/user-event";
import { FormProvider, useForm } from "react-hook-form";
import { axe } from "jest-axe";
import { Platform } from "@eait-playerexp-cn/metadata-types";
import { Buttons, InformationPageLabels } from "@src/Translations/InformationPageLabels";

const successToast = jest.fn();
jest.mock("@eait-playerexp-cn/core-ui-kit", () => ({
  ...jest.requireActual("@eait-playerexp-cn/core-ui-kit"),
  useToast: () => ({
    success: successToast
  })
}));



const FormWrapper = ({
  children,
  defaultValues = {}
}: {
  children: React.ReactNode;
  defaultValues?: any;
}) => {
  const methods = useForm({
    defaultValues,
    mode: "onChange"
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
};

describe("PlatformPreferencesForm", () => {
  const platforms: Platform[] = [
    {
      value: "pc",
      label: "PC",
      imageAsIcon: "/img/platforms/pc.png"
    },
    {
      value: "xbox",
      label: "Xbox",
      imageAsIcon: "/img/platforms/xbox.png"
    },
    {
      value: "playstation",
      label: "PlayStation",
      imageAsIcon: "/img/platforms/playstation.png"
    }
  ];

  const preferredPrimaryPlatforms: Platform = {
    value: "pc",
    label: "PC",
    imageAsIcon: "/img/platforms/pc.png"
  };

  const preferredSecondaryPlatforms: Platform[] = [
    {
      value: "xbox",
      label: "Xbox",
      imageAsIcon: "/img/platforms/xbox.png"
    }
  ];

  const infoLabels = InformationPageLabels.infoLabels;
  const props = {
    labels: {infoLabels, buttons: Buttons},
    platforms,
    preferredPrimaryPlatforms,
    preferredSecondaryPlatforms,
    onChange: jest.fn(),
    isSaved: false,
    isLoader: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should show platform preferences form correctly", () => {
    render(
      <FormWrapper>
        <PlatformPreferencesForm {...props} />
      </FormWrapper>
    );

    expect(screen.getByText("Platform Preferences")).toBeInTheDocument();
    expect(screen.getByText("Primary Platform")).toBeInTheDocument();
    expect(screen.getByText("Select your primary gaming platform")).toBeInTheDocument();
    expect(screen.getByText("Secondary Platforms")).toBeInTheDocument();
    expect(screen.getByText("Select additional platforms you use")).toBeInTheDocument();
  });

  it("should display primary platform in read-only mode initially", () => {
    render(
      <FormWrapper>
        <PlatformPreferencesForm {...props} />
      </FormWrapper>
    );

    expect(screen.getByText("Primary Platform")).toBeInTheDocument();
    expect(screen.getByText("Select your primary gaming platform")).toBeInTheDocument();

    // Check for primary platform tag specifically
    const primaryContainer = screen.getByText("Primary Platform").closest('.profile-primary-platform-container');
    expect(primaryContainer).toBeInTheDocument();

    // Check for platform image in primary section
    const platformImages = screen.getAllByAltText("Platform image");
    expect(platformImages.length).toBeGreaterThan(0);
  });

  it("should show edit button initially", () => {
    render(
      <FormWrapper>
        <PlatformPreferencesForm {...props} />
      </FormWrapper>
    );

    expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
  });

  it("should enable edit mode when edit button is clicked", async () => {
    render(
      <FormWrapper>
        <PlatformPreferencesForm {...props} />
      </FormWrapper>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Save" })).toBeInTheDocument();
  });

  it("should show platform select in edit mode", async () => {
    render(
      <FormWrapper>
        <PlatformPreferencesForm {...props} />
      </FormWrapper>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(screen.getByRole("button", { name: "PC" })).toBeInTheDocument();
  });

  it("should call onChange when edit mode is toggled", async () => {
    render(
      <FormWrapper>
        <PlatformPreferencesForm {...props} />
      </FormWrapper>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(props.onChange).toHaveBeenCalledTimes(1);
  });

  it("should exit edit mode when cancel button is clicked", async () => {
    render(
      <FormWrapper>
        <PlatformPreferencesForm {...props} />
      </FormWrapper>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    const cancelButton = screen.getByRole("button", { name: "Cancel" });
    await userEvent.click(cancelButton);

    expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
    expect(screen.queryByRole("button", { name: "Cancel" })).not.toBeInTheDocument();
    expect(screen.queryByRole("button", { name: "Save" })).not.toBeInTheDocument();
  });

  it("should exit edit mode when isSaved becomes true", async () => {
    const { rerender } = render(
      <FormWrapper>
        <PlatformPreferencesForm {...props} />
      </FormWrapper>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();

    const propsWithSaved = { ...props, isSaved: true };
    rerender(
      <FormWrapper>
        <PlatformPreferencesForm {...propsWithSaved} />
      </FormWrapper>
    );

    await waitFor(() => {
      expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
    });
  });

  it("should display secondary platforms correctly", () => {
    render(
      <FormWrapper>
        <PlatformPreferencesForm {...props} />
      </FormWrapper>
    );

    expect(screen.getByText("Secondary Platforms")).toBeInTheDocument();
    expect(screen.getByText("Select additional platforms you use")).toBeInTheDocument();

    // Check that all platforms are displayed in secondary section
    const secondaryPlatformTexts = screen.getAllByText(/PC|Xbox|PlayStation/);
    expect(secondaryPlatformTexts.length).toBeGreaterThan(0);
  });

  it("should show selected secondary platforms", () => {
    render(
      <FormWrapper>
        <PlatformPreferencesForm {...props} />
      </FormWrapper>
    );

    const xboxPlatform = platforms.find(p => p.value === "xbox");
    if (xboxPlatform) {
      expect(screen.getByText(xboxPlatform.label)).toBeInTheDocument();
    }
  });

  it("should handle platform selection change", async () => {
    render(
      <FormWrapper>
        <PlatformPreferencesForm {...props} />
      </FormWrapper>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    // Check that the select component is rendered
    const platformSelect = screen.getByRole("button", { name: "PC" });
    expect(platformSelect).toBeInTheDocument();

    // Click to open the dropdown
    await userEvent.click(platformSelect);

    // Check that options are available in the dropdown
    const selectOptions = screen.getAllByText(/PC|Xbox|PlayStation/);
    expect(selectOptions.length).toBeGreaterThan(3); // Should have options in both primary and secondary sections
  });

  it("should show success toast when information is saved", async () => {
    const propsWithSaved = {
      ...props,
      isSaved: true
    };

    const { unmount } = renderWithToast(
      <FormWrapper>
        <PlatformPreferencesForm {...propsWithSaved} />
      </FormWrapper>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(successToast).toHaveBeenCalledWith(
      expect.objectContaining({
        props: expect.objectContaining({
          header: infoLabels.success.updatedInformationHeader,
          content: infoLabels.success.platformPreferences
        })
      }),
      expect.objectContaining({
        autoClose: expect.any(Object)
      })
    );

    unmount();
  });

  it("should be accessible", async () => {
    let results: any;
    const { container } = render(
      <FormWrapper>
        <PlatformPreferencesForm {...props} />
      </FormWrapper>
    );

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });

  it("should handle missing onChange prop gracefully", () => {
    const propsWithoutOnChange = {
      ...props,
      onChange: undefined
    };

    expect(() => {
      render(
        <FormWrapper>
          <PlatformPreferencesForm {...propsWithoutOnChange} />
        </FormWrapper>
      );
    }).not.toThrow();
  });

  it("should be memoized and not re-render unnecessarily", () => {
    const { rerender } = render(
      <FormWrapper>
        <PlatformPreferencesForm {...props} />
      </FormWrapper>
    );

    rerender(
      <FormWrapper>
        <PlatformPreferencesForm {...props} />
      </FormWrapper>
    );

    expect(screen.getByText("Platform Preferences")).toBeInTheDocument();
    expect(screen.getByText("Primary Platform")).toBeInTheDocument();
    expect(screen.getByText("Secondary Platforms")).toBeInTheDocument();
  });
});

import { act, render, screen, waitFor } from "@testing-library/react";
import FranchiseYouPlayForm from "./FranchiseYouPlayForm";
import React from "react";
import { renderWithToast } from "@src/Helpers/Toast";
import userEvent from "@testing-library/user-event";
import { FormProvider, useForm } from "react-hook-form";
import { axe } from "jest-axe";
import { Franchise } from "@eait-playerexp-cn/metadata-types";
import { Buttons, InformationPageLabels } from "@src/Translations/InformationPageLabels";
import { FranchisesYouPlayLabels } from "../GamePreferencesPage";

const successToast = jest.fn();
jest.mock("@eait-playerexp-cn/core-ui-kit", () => ({
  ...jest.requireActual("@eait-playerexp-cn/core-ui-kit"),
  useToast: () => ({
    success: successToast
  })
}));

const FormWrapper = ({
  children,
  defaultValues = {}
}: {
  children: React.ReactNode;
  defaultValues?: any;
}) => {
  const methods = useForm({
    defaultValues,
    mode: "onChange"
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
};

describe("FranchiseYouPlayForm", () => {
  const franchises: Franchise[] = [
    {
      value: "fifa",
      label: "FIFA",
      image: "/img/franchises/fifa.png"
    },
    {
      value: "madden",
      label: "Madden NFL",
      image: "/img/franchises/madden.png"
    },
    {
      value: "apex",
      label: "Apex Legends",
      image: "/img/franchises/apex.png"
    }
  ];

  const preferredPrimaryFranchises: Franchise = {
    value: "fifa",
    label: "FIFA",
    image: "/img/franchises/fifa.png"
  };

  const preferredSecondaryFranchises: Franchise[] = [
    {
      value: "madden",
      label: "Madden NFL",
      image: "/img/franchises/madden.png"
    }
  ];

  const franchisesYouPlayLabels: FranchisesYouPlayLabels = {
    title: "Franchises You Play",
    description: "Select the game franchises you play",
    primaryFranchiseTitle: "Primary Franchise",
    primaryFranchiseSubTitle: "Select your main gaming franchise",
    secondaryFranchiseTitle: "Secondary Franchises",
    secondaryFranchiseSubTitle: "Select additional franchises you play",
    labels: {
      primaryFranchise: "Primary Franchise",
      loadMore: "Load More"
    },
    messages: {
      primaryFranchise: "Primary franchise is required",
      success: {
        header: "Franchises Updated",
        content: "Your franchise preferences have been updated successfully"
      }
    }
  };

  const props = {
    labels: { 
      franchisesYouPlayLabels,
      buttons: Buttons
    },    
    franchises,
    preferredPrimaryFranchises,
    preferredSecondaryFranchises,
    onChange: jest.fn(),
    isSaved: false,
    isLoader: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should show franchise you play form correctly", () => {
    render(
      <FormWrapper>
        <FranchiseYouPlayForm {...props} />
      </FormWrapper>
    );

    expect(screen.getByText("Franchises You Play")).toBeInTheDocument();
    expect(screen.getByText("Select the game franchises you play")).toBeInTheDocument();
    expect(screen.getByText("Primary Franchise")).toBeInTheDocument();
    expect(screen.getByText("Select your main gaming franchise")).toBeInTheDocument();
    expect(screen.getByText("Secondary Franchises")).toBeInTheDocument();
    expect(screen.getByText("Select additional franchises you play")).toBeInTheDocument();
  });

  it("should display primary franchise in read-only mode initially", () => {
    render(
      <FormWrapper>
        <FranchiseYouPlayForm {...props} />
      </FormWrapper>
    );

    expect(screen.getByText("Primary Franchise")).toBeInTheDocument();
    expect(screen.getByText("Select your main gaming franchise")).toBeInTheDocument();

    // Check for primary franchise tag specifically
    const primaryContainer = screen.getByText("Primary Franchise").closest('.profile-game-preferences-primary-container');
    expect(primaryContainer).toBeInTheDocument();

    // Check for franchise images
    const franchiseImages = screen.getAllByAltText("Franchise image");
    expect(franchiseImages.length).toBeGreaterThan(0);
  });

  it("should show edit button initially", () => {
    render(
      <FormWrapper>
        <FranchiseYouPlayForm {...props} />
      </FormWrapper>
    );

    expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
  });

  it("should enable edit mode when edit button is clicked", async () => {
    render(
      <FormWrapper>
        <FranchiseYouPlayForm {...props} />
      </FormWrapper>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Save" })).toBeInTheDocument();
  });

  it("should show franchise select in edit mode", async () => {
    render(
      <FormWrapper>
        <FranchiseYouPlayForm {...props} />
      </FormWrapper>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(screen.getByRole("combobox")).toBeInTheDocument();

    // Check that franchise options are available in the select
    const selectElement = screen.getByRole("combobox");
    expect(selectElement).toBeInTheDocument();

    // Check for MultiSelect component
    const multiSelectButton = screen.getByTestId("multi-select-id");
    expect(multiSelectButton).toBeInTheDocument();
  });

  it("should call onChange when edit mode is toggled", async () => {
    render(
      <FormWrapper>
        <FranchiseYouPlayForm {...props} />
      </FormWrapper>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(props.onChange).toHaveBeenCalledTimes(1);
  });

  it("should exit edit mode when cancel button is clicked", async () => {
    render(
      <FormWrapper>
        <FranchiseYouPlayForm {...props} />
      </FormWrapper>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    const cancelButton = screen.getByRole("button", { name: "Cancel" });
    await userEvent.click(cancelButton);

    expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
    expect(screen.queryByRole("button", { name: "Cancel" })).not.toBeInTheDocument();
    expect(screen.queryByRole("button", { name: "Save" })).not.toBeInTheDocument();
  });

  it("should exit edit mode when isSaved becomes true", async () => {
    const { rerender } = render(
      <FormWrapper>
        <FranchiseYouPlayForm {...props} />
      </FormWrapper>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();

    const propsWithSaved = { ...props, isSaved: true };
    rerender(
      <FormWrapper>
        <FranchiseYouPlayForm {...propsWithSaved} />
      </FormWrapper>
    );

    await waitFor(() => {
      expect(screen.getByRole("button", { name: "Edit" })).toBeInTheDocument();
    });
  });

  it("should display secondary franchises correctly", () => {
    render(
      <FormWrapper>
        <FranchiseYouPlayForm {...props} />
      </FormWrapper>
    );

    expect(screen.getByText("Secondary Franchises")).toBeInTheDocument();
    expect(screen.getByText("Select additional franchises you play")).toBeInTheDocument();

    // Check that secondary franchises section exists
    const secondaryContainer = screen.getByText("Secondary Franchises").closest('.profile-game-preferences-secondary-container');
    expect(secondaryContainer).toBeInTheDocument();

    // Check for secondary franchise options
    const secondaryFranchiseTexts = screen.getAllByText(/FIFA|Madden NFL|Apex Legends/);
    expect(secondaryFranchiseTexts.length).toBeGreaterThan(0);
  });

  it("should handle franchise selection functionality", async () => {
    render(
      <FormWrapper>
        <FranchiseYouPlayForm {...props} />
      </FormWrapper>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    const selectInput = screen.getByRole("combobox");
    await userEvent.selectOptions(selectInput, "fifa");

    expect(selectInput).toHaveValue("fifa");
  });

  it("should show success toast when information is saved", async () => {
    const propsWithSaved = {
      ...props,
      isSaved: true
    };

    const { unmount } = renderWithToast(
      <FormWrapper>
        <FranchiseYouPlayForm {...propsWithSaved} />
      </FormWrapper>
    );

    const editButton = screen.getByRole("button", { name: "Edit" });
    await userEvent.click(editButton);

    expect(successToast).toHaveBeenCalledWith(
      expect.objectContaining({
        props: expect.objectContaining({
          header: franchisesYouPlayLabels.messages.success.header,
          content: franchisesYouPlayLabels.messages.success.content
        })
      }),
      expect.objectContaining({
        autoClose: expect.any(Object)
      })
    );

    unmount();
  });

  it("should be accessible", async () => {
    let results: any;
    const { container } = render(
      <FormWrapper>
        <FranchiseYouPlayForm {...props} />
      </FormWrapper>
    );

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });

  it("should handle missing onChange prop gracefully", () => {
    const propsWithoutOnChange = {
      ...props,
      onChange: undefined
    };

    expect(() => {
      render(
        <FormWrapper>
          <FranchiseYouPlayForm {...propsWithoutOnChange} />
        </FormWrapper>
      );
    }).not.toThrow();
  });

  it("should handle empty franchises array gracefully", () => {
    const propsWithEmptyFranchises = {
      ...props,
      franchises: []
    };

    expect(() => {
      render(
        <FormWrapper>
          <FranchiseYouPlayForm {...propsWithEmptyFranchises} />
        </FormWrapper>
      );
    }).not.toThrow();
  });

  it("should handle different franchise selections correctly", () => {
    const differentPrimaryFranchise = {
      value: "apex",
      label: "Apex Legends",
      image: "/img/franchises/apex.png"
    };

    const propsWithDifferentFranchise = {
      ...props,
      preferredPrimaryFranchises: differentPrimaryFranchise
    };

    render(
      <FormWrapper>
        <FranchiseYouPlayForm {...propsWithDifferentFranchise} />
      </FormWrapper>
    );

    expect(screen.getByText("Apex Legends")).toBeInTheDocument();
    expect(screen.queryByText("FIFA")).not.toBeInTheDocument();
  });

  it("should be memoized and not re-render unnecessarily", () => {
    const { rerender } = render(
      <FormWrapper>
        <FranchiseYouPlayForm {...props} />
      </FormWrapper>
    );

    rerender(
      <FormWrapper>
        <FranchiseYouPlayForm {...props} />
      </FormWrapper>
    );

    expect(screen.getByText("Franchises You Play")).toBeInTheDocument();
    expect(screen.getByText("Primary Franchise")).toBeInTheDocument();
    expect(screen.getByText("Secondary Franchises")).toBeInTheDocument();
  });
});

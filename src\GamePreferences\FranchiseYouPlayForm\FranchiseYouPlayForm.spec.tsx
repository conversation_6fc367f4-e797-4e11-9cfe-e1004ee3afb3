import { act, render, screen, waitFor } from "@testing-library/react";
import FranchiseYouPlayForm from "./FranchiseYouPlayForm";
import React from "react";
import { renderWithToast } from "@src/Helpers/Toast";
import userEvent from "@testing-library/user-event";
import { FormProvider, useForm } from "react-hook-form";
import { axe } from "jest-axe";
import { Franchise } from "@eait-playerexp-cn/metadata-types";
import { Buttons, InformationPageLabels } from "@src/Translations/InformationPageLabels";
import { FranchisesYouPlayLabels } from "@src/Translations/FranchisesYouPlayLabels";

const successToast = jest.fn();
jest.mock("@eait-playerexp-cn/core-ui-kit", () => ({
  ...jest.requireActual("@eait-playerexp-cn/core-ui-kit"),
  useToast: () => ({
    success: successToast
  })
}));

const FormWrapper = ({
  children,
  defaultValues = {}
}: {
  children: React.ReactNode;
  defaultValues?: any;
}) => {
  const methods = useForm({
    defaultValues,
    mode: "onChange"
  });
  return <FormProvider {...methods}>{children}</FormProvider>;
};

describe("FranchiseYouPlayForm", () => {
  const franchises: Franchise[] = [
    {
      value: "fifa",
      label: "FIFA",
      image: "/img/franchises/fifa.png"
    },
    {
      value: "madden",
      label: "Madden NFL",
      image: "/img/franchises/madden.png"
    },
    {
      value: "apex",
      label: "Apex Legends",
      image: "/img/franchises/apex.png"
    }
  ];

  const preferredPrimaryFranchises: Franchise = {
    value: "fifa",
    label: "FIFA",
    image: "/img/franchises/fifa.png"
  };

  const preferredSecondaryFranchises: Franchise[] = [
    {
      value: "madden",
      label: "Madden NFL",
      image: "/img/franchises/madden.png"
    }
  ];

  const franchisesYouPlayLabels = FranchisesYouPlayLabels;
  const props = {
    labels: { 
      franchisesYouPlayLabels,
      buttons: Buttons
    },    
    franchises,
    preferredPrimaryFranchises,
    preferredSecondaryFranchises,
    onChange: jest.fn(),
    isLoader: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should show franchise you play form correctly", () => {
    render(
      <FormWrapper>
        <FranchiseYouPlayForm {...props} />
      </FormWrapper>
    );

    expect(screen.getByText(props.labels.franchisesYouPlayLabels.title)).toBeInTheDocument();
    expect(screen.getByText(props.labels.franchisesYouPlayLabels.description)).toBeInTheDocument();
    expect(screen.getByText(props.labels.franchisesYouPlayLabels.primaryFranchiseTitle)).toBeInTheDocument();
    expect(screen.getByText(props.labels.franchisesYouPlayLabels.primaryFranchiseSubTitle)).toBeInTheDocument();
    expect(screen.getByText(props.labels.franchisesYouPlayLabels.secondaryFranchiseTitle)).toBeInTheDocument();
    expect(screen.getByText(props.labels.franchisesYouPlayLabels.secondaryFranchiseSubTitle)).toBeInTheDocument();
    expect(screen.getByRole("button", { name: props.labels.buttons.edit })).toBeInTheDocument();
    expect(screen.getAllByRole("img" , { name: /Franchise image/i}).length).toBeGreaterThan(0);
  });

  it("should enable edit mode when edit button is clicked", async () => {
    render(
      <FormWrapper>
        <FranchiseYouPlayForm {...props} />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: props.labels.buttons.edit }));

    expect(screen.getByRole("button", { name: props.labels.buttons.cancel })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: props.labels.buttons.save })).toBeInTheDocument();
  });

  it("should show franchise select in edit mode", async () => {
    render(
      <FormWrapper>
        <FranchiseYouPlayForm {...props} />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: props.labels.buttons.edit }));

    expect(screen.getByRole("textbox", { name: /primaryFranchise/i })).toBeInTheDocument();
    expect(screen.getByTestId("multi-select-id")).toBeInTheDocument();
  });

  it("should call onChange when edit mode is toggled", async () => {
    render(
      <FormWrapper>
        <FranchiseYouPlayForm {...props} />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: props.labels.buttons.edit }));

    expect(props.onChange).toHaveBeenCalledTimes(1);
  });

  it("should use default image when franchise image doesn't have path", async () => {
    const secondaryFranchiseWithoutImage = [
    {
      value: "madden",
      label: "Madden NFL",
      image: undefined
    }
  ];
  render(
      <FormWrapper>
        <FranchiseYouPlayForm {...{...props, preferredSecondaryFranchises: secondaryFranchiseWithoutImage }} />
      </FormWrapper>
    );
    const secondaryFranchiseImage = screen.getByRole("img", { name: "Secondary franchise image"});
    expect(secondaryFranchiseImage).toBeInTheDocument();
    expect(secondaryFranchiseImage).toHaveAttribute("src", "/img/franchises-you-play/FIFA.png")
  })

  it("should exit edit mode when cancel button is clicked", async () => {
    render(
      <FormWrapper>
        <FranchiseYouPlayForm {...props} />
      </FormWrapper>
    );
    await userEvent.click(screen.getByRole("button", { name: props.labels.buttons.edit }));

    await userEvent.click(screen.getByRole("button", { name: props.labels.buttons.cancel }));

    expect(screen.getByRole("button", { name: props.labels.buttons.edit })).toBeInTheDocument();
    expect(screen.queryByRole("button", { name: props.labels.buttons.cancel })).not.toBeInTheDocument();
    expect(screen.queryByRole("button", { name: props.labels.buttons.save })).not.toBeInTheDocument();
  });

  it("should exit edit mode when isSaved becomes true", async () => {
    const { rerender } = render(
      <FormWrapper>
        <FranchiseYouPlayForm {...props} />
      </FormWrapper>
    );

    const editButton = screen.getByRole("button", { name: props.labels.buttons.edit });
    await userEvent.click(editButton);

    expect(screen.getByRole("button", { name: props.labels.buttons.cancel })).toBeInTheDocument();

    const propsWithSaved = { ...props, isSaved: true };
    rerender(
      <FormWrapper>
        <FranchiseYouPlayForm {...propsWithSaved} />
      </FormWrapper>
    );

    await waitFor(() => {
      expect(screen.getByRole("button", { name: props.labels.buttons.edit })).toBeInTheDocument();
    });
  });

  it("should handle franchise selection functionality", async () => {
    render(
      <FormWrapper>
        <FranchiseYouPlayForm {...props} />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: props.labels.buttons.edit }));

    const searchInput = screen.getByRole("textbox", { name: /primaryFranchise/i });
    await userEvent.clear(searchInput);
    await userEvent.type(searchInput, "FIFA");

    expect(searchInput).toHaveValue("FIFA");
  });

  it("should show success toast when information is saved", async () => {
    const propsWithSaved = {
      ...props,
      isSaved: true
    };

    const { unmount } = renderWithToast(
      <FormWrapper>
        <FranchiseYouPlayForm {...propsWithSaved} />
      </FormWrapper>
    );

    const editButton = screen.getByRole("button", { name: props.labels.buttons.edit });
    await userEvent.click(editButton);

    expect(successToast).toHaveBeenCalledWith(
      expect.objectContaining({
        props: expect.objectContaining({
          header: franchisesYouPlayLabels.messages.success.header,
          content: franchisesYouPlayLabels.messages.success.content
        })
      }),
      expect.objectContaining({
        autoClose: expect.any(Number)
      })
    );

    unmount();
  });

  it("should handle empty franchises array", () => {
    const propsWithEmptyFranchises = {
      ...props,
      franchises: []
    };

    expect(() => {
      render(
        <FormWrapper>
          <FranchiseYouPlayForm {...propsWithEmptyFranchises} />
        </FormWrapper>
      );
    }).not.toThrow();
  });

  it("should be accessible", async () => {
    let results;
    const { container } = render(
      <FormWrapper>
        <FranchiseYouPlayForm {...props} />
      </FormWrapper>
    );

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });
});

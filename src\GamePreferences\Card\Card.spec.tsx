import { render, screen } from "@testing-library/react";
import Card from "./Card";
import React from "react";
import { axe } from "jest-axe";

describe("Card", () => {
  it("should render card with children correctly", () => {
    render(
      <Card>
        <div>Test Content</div>
      </Card>
    );

    expect(screen.getByText("Test Content")).toBeInTheDocument();
    expect(screen.getByText("Test Content").parentElement).toHaveClass("empty-card");
  });

  it("should render card with multiple children", () => {
    render(
      <Card>
        <div>First Child</div>
        <div>Second Child</div>
        <span>Third Child</span>
      </Card>
    );

    expect(screen.getByText("First Child")).toBeInTheDocument();
    expect(screen.getByText("Second Child")).toBeInTheDocument();
    expect(screen.getByText("Third Child")).toBeInTheDocument();
  });

  it("should render empty card when no children provided", () => {
    const { container } = render(<Card {...{ children: null }} />);
    
    const cardElement = container.querySelector(".empty-card");
    expect(cardElement).toBeInTheDocument();
    expect(cardElement).toBeEmptyDOMElement();
  });

  it("should render card with complex JSX children", () => {
    render(
      <Card>
        <div>
          <h1>Title</h1>
          <p>Description</p>
          <button>Action</button>
        </div>
      </Card>
    );

    expect(screen.getByText("Title")).toBeInTheDocument();
    expect(screen.getByText("Description")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Action" })).toBeInTheDocument();
  });

  it("should render card with text content", () => {
    render(<Card>Simple text content</Card>);

    expect(screen.getByText("Simple text content")).toBeInTheDocument();
  });

  it("should render card with numeric children", () => {
    render(<Card>{123}</Card>);

    expect(screen.getByText("123")).toBeInTheDocument();
  });

  it("should handle null children gracefully", () => {
    render(<Card>{null}</Card>);
    
    const cardElement = document.querySelector(".empty-card");
    expect(cardElement).toBeInTheDocument();
  });

  it("should handle undefined children gracefully", () => {
    render(<Card>{undefined}</Card>);
    
    const cardElement = document.querySelector(".empty-card");
    expect(cardElement).toBeInTheDocument();
  });

  it("should handle boolean children gracefully", () => {
    render(<Card>{true}</Card>);
    
    const cardElement = document.querySelector(".empty-card");
    expect(cardElement).toBeInTheDocument();
  });

  it("should render card with conditional children", () => {
    const showContent = true;
    render(
      <Card>
        {showContent && <div>Conditional Content</div>}
        {!showContent && <div>Alternative Content</div>}
      </Card>
    );

    expect(screen.getByText("Conditional Content")).toBeInTheDocument();
    expect(screen.queryByText("Alternative Content")).not.toBeInTheDocument();
  });

  it("should be accessible", async () => {
    const { container } = render(
      <Card>
        <div>Accessible Content</div>
      </Card>
    );

    expect(await axe(container)).toHaveNoViolations();
  });

  it("should maintain proper DOM structure", () => {
    const { container } = render(
      <Card>
        <div data-testid="child">Child Element</div>
      </Card>
    );

    const cardElement = container.querySelector(".empty-card");
    const childElement = screen.getByTestId("child");
    
    expect(cardElement).toContainElement(childElement);
    expect(cardElement?.tagName).toBe("DIV");
  });

  it("should render card with array of children", () => {
    const items = ["Item 1", "Item 2", "Item 3"];
    render(
      <Card>
        {items.map((item, index) => (
          <div key={index}>{item}</div>
        ))}
      </Card>
    );

    expect(screen.getByText("Item 1")).toBeInTheDocument();
    expect(screen.getByText("Item 2")).toBeInTheDocument();
    expect(screen.getByText("Item 3")).toBeInTheDocument();
  });

  it("should render card with fragment children", () => {
    render(
      <Card>
        <>
          <div>Fragment Child 1</div>
          <div>Fragment Child 2</div>
        </>
      </Card>
    );

    expect(screen.getByText("Fragment Child 1")).toBeInTheDocument();
    expect(screen.getByText("Fragment Child 2")).toBeInTheDocument();
  });
});

import { render, screen } from "@testing-library/react";
import CheckboxCards, { CheckboxCardItem } from "./CheckboxCards";
import React from "react";
import userEvent from "@testing-library/user-event";
import { axe } from "jest-axe";

const mockIcon = () => <svg data-testid="mock-icon" />;

describe("CheckboxCards", () => {
  const mockItems: CheckboxCardItem[] = [
    {
      value: "item1",
      label: "Item 1",
      checked: false
    },
    {
      value: "item2",
      label: "Item 2",
      image: "/item2.jpg",
      checked: false
    },
    {
      value: "item3",
      label: "Item 3",
      icon: mockIcon,
      checked: true
    }
  ];

  const selectedItems: CheckboxCardItem[] = [
    {
      value: "item1",
      label: "Item 1",
      checked: true
    }
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should render checkbox cards correctly", () => {
    render(<CheckboxCards items={mockItems} />);

    expect(screen.getByText("Item 1")).toBeInTheDocument();
    expect(screen.getByText("Item 2")).toBeInTheDocument();
    expect(screen.getByText("Item 3")).toBeInTheDocument();
    expect(screen.getAllByRole("checkbox")).toHaveLength(3);
  });

  it("should render with pre-selected values", () => {
    render(<CheckboxCards items={mockItems} value={selectedItems} />);

    const checkboxes = screen.getAllByRole("checkbox");
    expect(checkboxes[0]).toBeChecked();
    expect(checkboxes[1]).not.toBeChecked();
    expect(checkboxes[2]).toBeChecked();
  });

  it("should handle empty items array", () => {
    render(<CheckboxCards items={[]} />);

    expect(screen.queryByRole("checkbox")).not.toBeInTheDocument();
    expect(document.querySelector(".card-container")).toBeInTheDocument();
  });

  it("should handle undefined items", () => {
    render(<CheckboxCards />);

    expect(screen.queryByRole("checkbox")).not.toBeInTheDocument();
    expect(document.querySelector(".card-container")).toBeInTheDocument();
  });

  it("should call onChange when item is selected", async () => {
    const onChange = jest.fn();
    render(<CheckboxCards items={mockItems} onChange={onChange} />);

    const firstCheckbox = screen.getByLabelText("Item 1");
    await userEvent.click(firstCheckbox);

    expect(onChange).toHaveBeenCalledWith([mockItems[0]]);
  });

  it("should call onChange when item is deselected", async () => {
    const onChange = jest.fn();
    render(<CheckboxCards items={mockItems} value={selectedItems} onChange={onChange} />);

    const firstCheckbox = screen.getByLabelText("Item 1");
    await userEvent.click(firstCheckbox);

    expect(onChange).toHaveBeenCalledWith([]);
  });

  it("should handle multiple selections", async () => {
    const onChange = jest.fn();
    render(<CheckboxCards items={mockItems} onChange={onChange} />);

    const firstCheckbox = screen.getByLabelText("Item 1");
    const secondCheckbox = screen.getByLabelText("Item 2");

    await userEvent.click(firstCheckbox);
    expect(onChange).toHaveBeenCalledWith([mockItems[0]]);

    await userEvent.click(secondCheckbox);
    expect(onChange).toHaveBeenCalledWith([mockItems[0], mockItems[1]]);
  });

  it("should handle selectAlternateItem mode", async () => {
    const onChange = jest.fn();
    const itemsForAlternate = [
      { value: "alt1", label: "Alt 1", checked: false },
      { value: "alt2", label: "Alt 2", checked: true }
    ];

    render(<CheckboxCards items={itemsForAlternate} onChange={onChange} selectAlternateItem />);

    const firstCheckbox = screen.getByLabelText("Alt 1");
    await userEvent.click(firstCheckbox);

    expect(onChange).toHaveBeenCalledWith([itemsForAlternate[0]]);
  });

  it("should handle selectAlternateItem mode when deselecting", async () => {
    const onChange = jest.fn();
    const itemsForAlternate = [
      { value: "alt1", label: "Alt 1", checked: false },
      { value: "alt2", label: "Alt 2", checked: true }
    ];

    render(<CheckboxCards items={itemsForAlternate} onChange={onChange} selectAlternateItem />);

    const secondCheckbox = screen.getByLabelText("Alt 2");
    await userEvent.click(secondCheckbox);

    expect(onChange).toHaveBeenCalledWith([itemsForAlternate[0]]);
  });

  it("should pass disabled prop to all checkbox cards", () => {
    render(<CheckboxCards items={mockItems} disabled />);

    const checkboxes = screen.getAllByRole("checkbox");
    checkboxes.forEach(checkbox => {
      expect(checkbox).toBeDisabled();
    });
  });

  it("should pass readOnly prop to all checkbox cards", async () => {
    const onChange = jest.fn();
    render(<CheckboxCards items={mockItems} onChange={onChange} readOnly />);

    const firstCheckbox = screen.getByLabelText("Item 1");
    await userEvent.click(firstCheckbox);

    expect(onChange).not.toHaveBeenCalled();
  });

  it("should handle missing onChange prop gracefully", async () => {
    expect(() => {
      render(<CheckboxCards items={mockItems} />);
    }).not.toThrow();

    const firstCheckbox = screen.getByLabelText("Item 1");
    await userEvent.click(firstCheckbox);
  });

  it("should maintain internal state when no value prop provided", async () => {
    const onChange = jest.fn();
    render(<CheckboxCards items={mockItems} onChange={onChange} />);

    const firstCheckbox = screen.getByLabelText("Item 1");
    const secondCheckbox = screen.getByLabelText("Item 2");

    await userEvent.click(firstCheckbox);
    await userEvent.click(secondCheckbox);

    expect(onChange).toHaveBeenCalledTimes(2);
    expect(onChange).toHaveBeenLastCalledWith([mockItems[0], mockItems[1]]);
  });

  it("should remove item from selection when unchecked", async () => {
    const onChange = jest.fn();
    const initialValue = [mockItems[0], mockItems[1]];
    render(<CheckboxCards items={mockItems} value={initialValue} onChange={onChange} />);

    const firstCheckbox = screen.getByLabelText("Item 1");
    await userEvent.click(firstCheckbox);

    expect(onChange).toHaveBeenCalledWith([mockItems[1]]);
  });

  it("should handle items with different properties", () => {
    const diverseItems: CheckboxCardItem[] = [
      { value: "text", label: "Text Only" },
      { value: "image", label: "With Image", image: "/image.jpg" },
      { value: "icon", label: "With Icon", icon: mockIcon },
      { value: "imageAsIcon", label: "Image As Icon", imageAsIcon: "/icon.png" }
    ];

    render(<CheckboxCards items={diverseItems} />);

    expect(screen.getByText("Text Only")).toBeInTheDocument();
    expect(screen.getByText("With Image")).toBeInTheDocument();
    expect(screen.getByText("With Icon")).toBeInTheDocument();
    expect(screen.getByText("Image As Icon")).toBeInTheDocument();
  });

  it("should forward ref correctly", () => {
    const ref = React.createRef<HTMLDivElement>();
    render(<CheckboxCards ref={ref} items={mockItems} />);

    expect(ref.current).toBeInstanceOf(HTMLDivElement);
    expect(ref.current).toHaveClass("card-container");
  });

  it("should be accessible", async () => {
    const { container } = render(<CheckboxCards items={mockItems} />);

    expect(await axe(container)).toHaveNoViolations();
  });

  it("should render correct number of checkbox cards", () => {
    render(<CheckboxCards items={mockItems} />);

    const cardContainer = document.querySelector(".card-container");
    expect(cardContainer?.children).toHaveLength(3);
  });

  it("should handle complex selection scenarios", async () => {
    const onChange = jest.fn();
    const complexItems = [
      { value: "a", label: "A", checked: false },
      { value: "b", label: "B", checked: true },
      { value: "c", label: "C", checked: false }
    ];

    render(<CheckboxCards items={complexItems} onChange={onChange} />);

    const checkboxA = screen.getByLabelText("A");
    const checkboxB = screen.getByLabelText("B");
    const checkboxC = screen.getByLabelText("C");

    await userEvent.click(checkboxA);
    expect(onChange).toHaveBeenCalledWith([complexItems[0]]);

    await userEvent.click(checkboxC);
    expect(onChange).toHaveBeenCalledWith([complexItems[0], complexItems[2]]);

    await userEvent.click(checkboxB);
    expect(onChange).toHaveBeenCalledWith([complexItems[0], complexItems[2], complexItems[1]]);
  });

  it("should handle edge case with empty checked array", async () => {
    const onChange = jest.fn();
    render(<CheckboxCards items={mockItems} value={[]} onChange={onChange} />);

    const firstCheckbox = screen.getByLabelText("Item 1");
    await userEvent.click(firstCheckbox);

    expect(onChange).toHaveBeenCalledWith([mockItems[0]]);
  });
});

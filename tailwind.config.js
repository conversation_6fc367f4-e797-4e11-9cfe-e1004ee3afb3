const coreUiKit = require("@eait-playerexp-cn/core-ui-kit/config");

module.exports = coreUiKit({
  content: ["src/**/*.{ts,tsx}","src/**/**/*.{ts,tsx}"],
  plugins: [require("@tailwindcss/typography")],
  theme: {
    extend: {
      boxShadow: {
        sm: "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
        "gray-10-outline-10-opacity-0.7": "0px 0px 10px rgba(255, 255, 255, 0.7)",
        "navy-60-outline-60-opacity-0.8": "0px 0px 60px rgba(42, 59, 137, 0.8)",
        "gray-90-outline-4-8-opacity-0.15": " 0px 4px 8px 0px rgba(0, 0, 0, 0.15)",
        "navy-80-image-card": "0 4px 8px 0 rgb(16 14 134), 0 6px 20px 0 rgb(0 3 10)",
        "error-background-image": "var(--error-desktop-background-image)",
        "down-arrow-icon-background": "var(--down-arrow-icon-background)"
      },
      fill: (theme) => ({
        current: "currentColor",
        "gray-10": theme("colors.gray.10"),
        "gray-50": theme("colors.gray.50"),
        "indigo-50": theme("colors.indigo.50"),
        "navy-60": theme("colors.navy.60"),
        "navy-80": theme("colors.navy.80"),
        "success-30": theme("colors.success.30"),
        "success-70": theme("colors.success.70")
      })
    }
  }
});

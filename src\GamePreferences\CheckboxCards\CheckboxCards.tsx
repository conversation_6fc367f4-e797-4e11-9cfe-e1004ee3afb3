import { SvgProps } from "@eait-playerexp-cn/core-ui-kit";
import CheckboxCard from "./CheckboxCard";
import React, { forwardRef, useState } from "react";

export type CheckboxCardItem = {
  value: string;
  label: string;
  image?: string;
  imageAsIcon?: string;
  checked?: boolean;
  icon?: React.FC<SvgProps>;
}

export interface CheckboxCardsProps {
  items?: CheckboxCardItem[];
  value?: CheckboxCardItem[];
  disabled?: boolean;
  onChange?: (selectedData: CheckboxCardItem[]) => void;
  readOnly?: boolean;
  selectAlternateItem?: boolean;
}

const CheckboxCards = forwardRef<HTMLDivElement, CheckboxCardsProps>(
  ({ items = [], value, disabled, onChange, readOnly = false, selectAlternateItem }, ref) => {
    const [checked, setChecked] = useState<CheckboxCardItem[]>(value || []);

    function checkboxOnChange(data: CheckboxCardItem, isChecked: boolean): void {
      let selectedData: CheckboxCardItem[];
      if (selectAlternateItem) {
        if (!data.checked) {
          selectedData = [data];
        } else {
          selectedData = items.filter((item) => item !== data && [item]);
        }
      } else {
        if (isChecked) {
          selectedData = checked.length !== 0 ? [...checked, data] : [data];
        } else {
          selectedData = [...checked.filter((item) => item.value !== data.value)];
        }
      }
      onChange && onChange(selectedData);
      setChecked(selectedData);
    }

    return (
      <div className="card-container" ref={ref}>
        {items.map((item, key) => (
          <CheckboxCard key={key} {...{ disabled, item, onChange: checkboxOnChange, readOnly }} />
        ))}
      </div>
    );
  }
);

CheckboxCards.displayName = 'CheckboxCards';
export default CheckboxCards;
